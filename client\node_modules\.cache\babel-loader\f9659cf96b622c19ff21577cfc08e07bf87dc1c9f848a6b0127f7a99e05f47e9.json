{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { Suspense, lazy } from \"react\";\nimport \"./stylesheets/theme.css\";\nimport \"./stylesheets/alignments.css\";\nimport \"./stylesheets/textelements.css\";\nimport \"./stylesheets/form-elements.css\";\nimport \"./stylesheets/custom-components.css\";\nimport \"./stylesheets/layout.css\";\nimport \"./styles/modern.css\";\nimport \"./styles/animations.css\";\nimport { BrowserRouter, Routes, Route } from \"react-router-dom\";\nimport ProtectedRoute from \"./components/ProtectedRoute\";\nimport Loader from \"./components/Loader\";\nimport { useSelector } from \"react-redux\";\nimport { ThemeProvider } from \"./contexts/ThemeContext\";\nimport { ErrorBoundary } from \"./components/modern\";\nimport AdminProtectedRoute from \"./components/AdminProtectedRoute\";\n\n// Immediate load components (critical for initial render)\nimport Login from \"./pages/common/Login\";\nimport Register from \"./pages/common/Register\";\nimport Home from \"./pages/common/Home\";\n\n// Lazy load components for better performance\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Quiz = /*#__PURE__*/lazy(_c = () => import(\"./pages/user/Quiz\"));\n_c2 = Quiz;\nconst QuizPlay = /*#__PURE__*/lazy(_c3 = () => import(\"./pages/user/Quiz/QuizPlay\"));\n_c4 = QuizPlay;\nconst QuizResult = /*#__PURE__*/lazy(_c5 = () => import(\"./pages/user/Quiz/QuizResult\"));\n_c6 = QuizResult;\nconst Exams = /*#__PURE__*/lazy(_c7 = () => import(\"./pages/admin/Exams\"));\n_c8 = Exams;\nconst AddEditExam = /*#__PURE__*/lazy(_c9 = () => import(\"./pages/admin/Exams/AddEditExam\"));\n_c10 = AddEditExam;\nconst Users = /*#__PURE__*/lazy(_c11 = () => import(\"./pages/admin/Users\"));\n_c12 = Users;\nconst AdminDashboard = /*#__PURE__*/lazy(_c13 = () => import(\"./pages/admin/Dashboard\"));\n_c14 = AdminDashboard;\nconst TrialPage = /*#__PURE__*/lazy(_c15 = () => import(\"./pages/trial/TrialPage\"));\n_c16 = TrialPage;\nconst WriteExam = /*#__PURE__*/lazy(_c17 = () => import(\"./pages/user/WriteExam\"));\n_c18 = WriteExam;\nconst UserReports = /*#__PURE__*/lazy(_c19 = () => import(\"./pages/user/UserReports\"));\n_c20 = UserReports;\nconst AdminReports = /*#__PURE__*/lazy(_c21 = () => import(\"./pages/admin/AdminReports\"));\n_c22 = AdminReports;\nconst StudyMaterial = /*#__PURE__*/lazy(_c23 = () => import(\"./pages/user/StudyMaterial\"));\n_c24 = StudyMaterial;\nconst VideoLessons = /*#__PURE__*/lazy(_c25 = () => import(\"./pages/user/VideoLessons\"));\n_c26 = VideoLessons;\nconst Ranking = /*#__PURE__*/lazy(_c27 = () => import(\"./pages/user/Ranking\"));\n_c28 = Ranking;\nconst RankingErrorBoundary = /*#__PURE__*/lazy(_c29 = () => import(\"./components/RankingErrorBoundary\"));\n_c30 = RankingErrorBoundary;\nconst Profile = /*#__PURE__*/lazy(_c31 = () => import(\"./pages/common/Profile\"));\n_c32 = Profile;\nconst AboutUs = /*#__PURE__*/lazy(_c33 = () => import(\"./pages/user/AboutUs\"));\n_c34 = AboutUs;\nconst Forum = /*#__PURE__*/lazy(_c35 = () => import(\"./pages/common/Forum\"));\n_c36 = Forum;\nconst Test = /*#__PURE__*/lazy(_c37 = () => import(\"./pages/user/Test\"));\n_c38 = Test;\nconst Subscription = /*#__PURE__*/lazy(_c39 = () => import(\"./pages/user/Subscription\"));\n_c40 = Subscription;\nconst Hub = /*#__PURE__*/lazy(_c41 = () => import(\"./pages/user/Hub\"));\n_c42 = Hub;\nconst AdminStudyMaterials = /*#__PURE__*/lazy(_c43 = () => import(\"./pages/admin/StudyMaterials\"));\n_c44 = AdminStudyMaterials;\nconst AdminNotifications = /*#__PURE__*/lazy(_c45 = () => import(\"./pages/admin/Notifications/AdminNotifications\"));\n_c46 = AdminNotifications;\nconst AdminForum = /*#__PURE__*/lazy(_c47 = () => import(\"./pages/admin/Forum\"));\n_c48 = AdminForum;\nconst DebugAuth = /*#__PURE__*/lazy(_c49 = () => import(\"./components/DebugAuth\"));\n_c50 = DebugAuth;\nconst RankingDemo = /*#__PURE__*/lazy(_c51 = () => import(\"./components/modern/RankingDemo\"));\n\n// Global error handler for CSS style errors and null reference errors\n_c52 = RankingDemo;\nwindow.addEventListener('error', event => {\n  if (event.message && (event.message.includes('Indexed property setter is not supported') || event.message.includes('Cannot read properties of null') || event.message.includes('Cannot read property \\'style\\''))) {\n    console.warn('DOM/Style Error caught and handled:', event.message);\n    event.preventDefault();\n    return false;\n  }\n});\n\n// Handle unhandled promise rejections that might be related to style errors\nwindow.addEventListener('unhandledrejection', event => {\n  if (event.reason && event.reason.message && (event.reason.message.includes('Indexed property setter is not supported') || event.reason.message.includes('Cannot read properties of null') || event.reason.message.includes('Cannot read property \\'style\\''))) {\n    console.warn('DOM/Style Promise Rejection caught and handled:', event.reason.message);\n    event.preventDefault();\n  }\n});\n// Fast loading component for lazy routes\nconst FastLoader = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 rounded-full h-12 w-12 border-t-2 border-blue-300 mx-auto animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-600 font-medium\",\n      children: \"Loading page...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-400 text-sm mt-2\",\n      children: \"Please wait a moment\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 78,\n  columnNumber: 3\n}, this);\n_c53 = FastLoader;\nfunction App() {\n  _s();\n  const {\n    loading\n  } = useSelector(state => state.loader);\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      children: [loading && /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(BrowserRouter, {\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/register\",\n            element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 44\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 36\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/ranking-demo\",\n            element: /*#__PURE__*/_jsxDEV(RankingDemo, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 48\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/trial\",\n            element: /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 33\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(TrialPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/test\",\n            element: /*#__PURE__*/_jsxDEV(Suspense, {\n              fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 33\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Test, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/forum\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Forum, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/profile\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/profile\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/subscription\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Subscription, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/subscription\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Subscription, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/hub\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Hub, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/quiz\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Quiz, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/write-exam/:id\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(WriteExam, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/quiz/:id/result\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(QuizResult, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/quiz/:id/play\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(QuizPlay, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/reports\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(UserReports, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/study-material\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(StudyMaterial, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/video-lessons\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(VideoLessons, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/ranking\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 37\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(RankingErrorBoundary, {\n                  children: /*#__PURE__*/_jsxDEV(Ranking, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user/about-us\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AboutUs, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/dashboard\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/users\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Users, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/exams\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Exams, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/exams/add\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AddEditExam, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/exams/edit/:id\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AddEditExam, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/study-materials\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminStudyMaterials, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/reports\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminReports, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/notifications\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminNotifications, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/forum\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminForum, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/debug\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(DebugAuth, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"qPH7E8ZBRBZG+ChS5bGqdjAK4Pc=\", false, function () {\n  return [useSelector];\n});\n_c54 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36, _c37, _c38, _c39, _c40, _c41, _c42, _c43, _c44, _c45, _c46, _c47, _c48, _c49, _c50, _c51, _c52, _c53, _c54;\n$RefreshReg$(_c, \"Quiz$lazy\");\n$RefreshReg$(_c2, \"Quiz\");\n$RefreshReg$(_c3, \"QuizPlay$lazy\");\n$RefreshReg$(_c4, \"QuizPlay\");\n$RefreshReg$(_c5, \"QuizResult$lazy\");\n$RefreshReg$(_c6, \"QuizResult\");\n$RefreshReg$(_c7, \"Exams$lazy\");\n$RefreshReg$(_c8, \"Exams\");\n$RefreshReg$(_c9, \"AddEditExam$lazy\");\n$RefreshReg$(_c10, \"AddEditExam\");\n$RefreshReg$(_c11, \"Users$lazy\");\n$RefreshReg$(_c12, \"Users\");\n$RefreshReg$(_c13, \"AdminDashboard$lazy\");\n$RefreshReg$(_c14, \"AdminDashboard\");\n$RefreshReg$(_c15, \"TrialPage$lazy\");\n$RefreshReg$(_c16, \"TrialPage\");\n$RefreshReg$(_c17, \"WriteExam$lazy\");\n$RefreshReg$(_c18, \"WriteExam\");\n$RefreshReg$(_c19, \"UserReports$lazy\");\n$RefreshReg$(_c20, \"UserReports\");\n$RefreshReg$(_c21, \"AdminReports$lazy\");\n$RefreshReg$(_c22, \"AdminReports\");\n$RefreshReg$(_c23, \"StudyMaterial$lazy\");\n$RefreshReg$(_c24, \"StudyMaterial\");\n$RefreshReg$(_c25, \"VideoLessons$lazy\");\n$RefreshReg$(_c26, \"VideoLessons\");\n$RefreshReg$(_c27, \"Ranking$lazy\");\n$RefreshReg$(_c28, \"Ranking\");\n$RefreshReg$(_c29, \"RankingErrorBoundary$lazy\");\n$RefreshReg$(_c30, \"RankingErrorBoundary\");\n$RefreshReg$(_c31, \"Profile$lazy\");\n$RefreshReg$(_c32, \"Profile\");\n$RefreshReg$(_c33, \"AboutUs$lazy\");\n$RefreshReg$(_c34, \"AboutUs\");\n$RefreshReg$(_c35, \"Forum$lazy\");\n$RefreshReg$(_c36, \"Forum\");\n$RefreshReg$(_c37, \"Test$lazy\");\n$RefreshReg$(_c38, \"Test\");\n$RefreshReg$(_c39, \"Subscription$lazy\");\n$RefreshReg$(_c40, \"Subscription\");\n$RefreshReg$(_c41, \"Hub$lazy\");\n$RefreshReg$(_c42, \"Hub\");\n$RefreshReg$(_c43, \"AdminStudyMaterials$lazy\");\n$RefreshReg$(_c44, \"AdminStudyMaterials\");\n$RefreshReg$(_c45, \"AdminNotifications$lazy\");\n$RefreshReg$(_c46, \"AdminNotifications\");\n$RefreshReg$(_c47, \"AdminForum$lazy\");\n$RefreshReg$(_c48, \"AdminForum\");\n$RefreshReg$(_c49, \"DebugAuth$lazy\");\n$RefreshReg$(_c50, \"DebugAuth\");\n$RefreshReg$(_c51, \"RankingDemo$lazy\");\n$RefreshReg$(_c52, \"RankingDemo\");\n$RefreshReg$(_c53, \"FastLoader\");\n$RefreshReg$(_c54, \"App\");", "map": {"version": 3, "names": ["React", "Suspense", "lazy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "ProtectedRoute", "Loader", "useSelector", "ThemeProvider", "Error<PERSON>ou<PERSON><PERSON>", "AdminProtectedRoute", "<PERSON><PERSON>", "Register", "Home", "jsxDEV", "_jsxDEV", "Quiz", "_c", "_c2", "QuizPlay", "_c3", "_c4", "QuizResult", "_c5", "_c6", "<PERSON><PERSON>", "_c7", "_c8", "AddEditExam", "_c9", "_c10", "Users", "_c11", "_c12", "AdminDashboard", "_c13", "_c14", "TrialPage", "_c15", "_c16", "WriteExam", "_c17", "_c18", "UserReports", "_c19", "_c20", "AdminReports", "_c21", "_c22", "StudyMaterial", "_c23", "_c24", "VideoLessons", "_c25", "_c26", "Ranking", "_c27", "_c28", "RankingError<PERSON><PERSON><PERSON>ry", "_c29", "_c30", "Profile", "_c31", "_c32", "AboutUs", "_c33", "_c34", "Forum", "_c35", "_c36", "Test", "_c37", "_c38", "Subscription", "_c39", "_c40", "<PERSON><PERSON>", "_c41", "_c42", "AdminStudyMaterials", "_c43", "_c44", "AdminNotifications", "_c45", "_c46", "AdminForum", "_c47", "_c48", "DebugAuth", "_c49", "_c50", "RankingDemo", "_c51", "_c52", "window", "addEventListener", "event", "message", "includes", "console", "warn", "preventDefault", "reason", "FastLoader", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c53", "App", "_s", "loading", "state", "loader", "path", "element", "fallback", "_c54", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/App.js"], "sourcesContent": ["import React, { Suspense, lazy } from \"react\";\r\nimport \"./stylesheets/theme.css\";\r\nimport \"./stylesheets/alignments.css\";\r\nimport \"./stylesheets/textelements.css\";\r\nimport \"./stylesheets/form-elements.css\";\r\nimport \"./stylesheets/custom-components.css\";\r\nimport \"./stylesheets/layout.css\";\r\nimport \"./styles/modern.css\";\r\nimport \"./styles/animations.css\";\r\nimport { BrowserRouter, Routes, Route } from \"react-router-dom\";\r\nimport ProtectedRoute from \"./components/ProtectedRoute\";\r\nimport Loader from \"./components/Loader\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { ThemeProvider } from \"./contexts/ThemeContext\";\r\nimport { ErrorBoundary } from \"./components/modern\";\r\nimport AdminProtectedRoute from \"./components/AdminProtectedRoute\";\r\n\r\n// Immediate load components (critical for initial render)\r\nimport Login from \"./pages/common/Login\";\r\nimport Register from \"./pages/common/Register\";\r\nimport Home from \"./pages/common/Home\";\r\n\r\n// Lazy load components for better performance\r\nconst Quiz = lazy(() => import(\"./pages/user/Quiz\"));\r\nconst QuizPlay = lazy(() => import(\"./pages/user/Quiz/QuizPlay\"));\r\nconst QuizResult = lazy(() => import(\"./pages/user/Quiz/QuizResult\"));\r\nconst Exams = lazy(() => import(\"./pages/admin/Exams\"));\r\nconst AddEditExam = lazy(() => import(\"./pages/admin/Exams/AddEditExam\"));\r\nconst Users = lazy(() => import(\"./pages/admin/Users\"));\r\nconst AdminDashboard = lazy(() => import(\"./pages/admin/Dashboard\"));\r\nconst TrialPage = lazy(() => import(\"./pages/trial/TrialPage\"));\r\nconst WriteExam = lazy(() => import(\"./pages/user/WriteExam\"));\r\nconst UserReports = lazy(() => import(\"./pages/user/UserReports\"));\r\nconst AdminReports = lazy(() => import(\"./pages/admin/AdminReports\"));\r\nconst StudyMaterial = lazy(() => import(\"./pages/user/StudyMaterial\"));\r\nconst VideoLessons = lazy(() => import(\"./pages/user/VideoLessons\"));\r\nconst Ranking = lazy(() => import(\"./pages/user/Ranking\"));\r\nconst RankingErrorBoundary = lazy(() => import(\"./components/RankingErrorBoundary\"));\r\nconst Profile = lazy(() => import(\"./pages/common/Profile\"));\r\nconst AboutUs = lazy(() => import(\"./pages/user/AboutUs\"));\r\nconst Forum = lazy(() => import(\"./pages/common/Forum\"));\r\nconst Test = lazy(() => import(\"./pages/user/Test\"));\r\nconst Subscription = lazy(() => import(\"./pages/user/Subscription\"));\r\n\r\nconst Hub = lazy(() => import(\"./pages/user/Hub\"));\r\nconst AdminStudyMaterials = lazy(() => import(\"./pages/admin/StudyMaterials\"));\r\nconst AdminNotifications = lazy(() => import(\"./pages/admin/Notifications/AdminNotifications\"));\r\nconst AdminForum = lazy(() => import(\"./pages/admin/Forum\"));\r\nconst DebugAuth = lazy(() => import(\"./components/DebugAuth\"));\r\nconst RankingDemo = lazy(() => import(\"./components/modern/RankingDemo\"));\r\n\r\n// Global error handler for CSS style errors and null reference errors\r\nwindow.addEventListener('error', (event) => {\r\n  if (event.message && (\r\n    event.message.includes('Indexed property setter is not supported') ||\r\n    event.message.includes('Cannot read properties of null') ||\r\n    event.message.includes('Cannot read property \\'style\\'')\r\n  )) {\r\n    console.warn('DOM/Style Error caught and handled:', event.message);\r\n    event.preventDefault();\r\n    return false;\r\n  }\r\n});\r\n\r\n// Handle unhandled promise rejections that might be related to style errors\r\nwindow.addEventListener('unhandledrejection', (event) => {\r\n  if (event.reason && event.reason.message && (\r\n    event.reason.message.includes('Indexed property setter is not supported') ||\r\n    event.reason.message.includes('Cannot read properties of null') ||\r\n    event.reason.message.includes('Cannot read property \\'style\\'')\r\n  )) {\r\n    console.warn('DOM/Style Promise Rejection caught and handled:', event.reason.message);\r\n    event.preventDefault();\r\n  }\r\n});\r\n// Fast loading component for lazy routes\r\nconst FastLoader = () => (\r\n  <div className=\"flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\r\n    <div className=\"text-center\">\r\n      <div className=\"relative\">\r\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n        <div className=\"absolute inset-0 rounded-full h-12 w-12 border-t-2 border-blue-300 mx-auto animate-pulse\"></div>\r\n      </div>\r\n      <p className=\"text-gray-600 font-medium\">Loading page...</p>\r\n      <p className=\"text-gray-400 text-sm mt-2\">Please wait a moment</p>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nfunction App() {\r\n  const { loading } = useSelector((state) => state.loader);\r\n  return (\r\n    <ErrorBoundary>\r\n      <ThemeProvider>\r\n        {loading && <Loader />}\r\n        <BrowserRouter>\r\n        <Routes>\r\n          {/* Common Routes */}\r\n          <Route path=\"/login\" element={<Login />} />\r\n          <Route path=\"/register\" element={<Register />} />\r\n          <Route path=\"/\" element={<Home />} />\r\n          <Route path=\"/ranking-demo\" element={<RankingDemo />} />\r\n\r\n          {/* Trial Route (No authentication required) */}\r\n          <Route path=\"/trial\" element={\r\n            <Suspense fallback={<FastLoader />}>\r\n              <TrialPage />\r\n            </Suspense>\r\n          } />\r\n\r\n          <Route path=\"/test\" element={\r\n            <Suspense fallback={<FastLoader />}>\r\n              <Test />\r\n            </Suspense>\r\n          } />\r\n          <Route\r\n            path=\"/forum\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Forum />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* User Routes */}\r\n          <Route\r\n            path=\"/profile\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Profile />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/profile\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Profile />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/subscription\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Subscription />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/subscription\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Subscription />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/user/hub\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Hub />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/quiz\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Quiz />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/user/write-exam/:id\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <WriteExam />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/quiz/:id/result\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <QuizResult />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* New Quiz Routes */}\r\n\r\n          <Route\r\n            path=\"/quiz/:id/play\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <QuizPlay />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/user/reports\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <UserReports />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/study-material\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <StudyMaterial />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/video-lessons\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <VideoLessons />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/ranking\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <RankingErrorBoundary>\r\n                    <Ranking />\r\n                  </RankingErrorBoundary>\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/about-us\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AboutUs />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* Admin Routes */}\r\n          <Route\r\n            path=\"/admin/dashboard\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminDashboard />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/users\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <Users />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/exams\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <Exams />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/exams/add\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AddEditExam />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/exams/edit/:id\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AddEditExam />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/study-materials\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminStudyMaterials />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/reports\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminReports />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/notifications\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminNotifications />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/forum\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminForum />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/debug\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <DebugAuth />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n        </Routes>\r\n      </BrowserRouter>\r\n    </ThemeProvider>\r\n    </ErrorBoundary>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,IAAI,QAAQ,OAAO;AAC7C,OAAO,yBAAyB;AAChC,OAAO,8BAA8B;AACrC,OAAO,gCAAgC;AACvC,OAAO,iCAAiC;AACxC,OAAO,qCAAqC;AAC5C,OAAO,0BAA0B;AACjC,OAAO,qBAAqB;AAC5B,OAAO,yBAAyB;AAChC,SAASC,aAAa,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAC/D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,OAAOC,mBAAmB,MAAM,kCAAkC;;AAElE;AACA,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,OAAOC,IAAI,MAAM,qBAAqB;;AAEtC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,IAAI,gBAAGf,IAAI,CAAAgB,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,GAAA,GAA/CF,IAAI;AACV,MAAMG,QAAQ,gBAAGlB,IAAI,CAAAmB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC;AAACC,GAAA,GAA5DF,QAAQ;AACd,MAAMG,UAAU,gBAAGrB,IAAI,CAAAsB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC;AAACC,GAAA,GAAhEF,UAAU;AAChB,MAAMG,KAAK,gBAAGxB,IAAI,CAAAyB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,GAAA,GAAlDF,KAAK;AACX,MAAMG,WAAW,gBAAG3B,IAAI,CAAA4B,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC;AAACC,IAAA,GAApEF,WAAW;AACjB,MAAMG,KAAK,gBAAG9B,IAAI,CAAA+B,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,IAAA,GAAlDF,KAAK;AACX,MAAMG,cAAc,gBAAGjC,IAAI,CAAAkC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;AAACC,IAAA,GAA/DF,cAAc;AACpB,MAAMG,SAAS,gBAAGpC,IAAI,CAAAqC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;AAACC,IAAA,GAA1DF,SAAS;AACf,MAAMG,SAAS,gBAAGvC,IAAI,CAAAwC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAACC,IAAA,GAAzDF,SAAS;AACf,MAAMG,WAAW,gBAAG1C,IAAI,CAAA2C,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC;AAACC,IAAA,GAA7DF,WAAW;AACjB,MAAMG,YAAY,gBAAG7C,IAAI,CAAA8C,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC;AAACC,IAAA,GAAhEF,YAAY;AAClB,MAAMG,aAAa,gBAAGhD,IAAI,CAAAiD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC;AAACC,IAAA,GAAjEF,aAAa;AACnB,MAAMG,YAAY,gBAAGnD,IAAI,CAAAoD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC;AAACC,IAAA,GAA/DF,YAAY;AAClB,MAAMG,OAAO,gBAAGtD,IAAI,CAAAuD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAArDF,OAAO;AACb,MAAMG,oBAAoB,gBAAGzD,IAAI,CAAA0D,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAAC;AAACC,IAAA,GAA/EF,oBAAoB;AAC1B,MAAMG,OAAO,gBAAG5D,IAAI,CAAA6D,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAACC,IAAA,GAAvDF,OAAO;AACb,MAAMG,OAAO,gBAAG/D,IAAI,CAAAgE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAArDF,OAAO;AACb,MAAMG,KAAK,gBAAGlE,IAAI,CAAAmE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAAnDF,KAAK;AACX,MAAMG,IAAI,gBAAGrE,IAAI,CAAAsE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAA/CF,IAAI;AACV,MAAMG,YAAY,gBAAGxE,IAAI,CAAAyE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC;AAACC,IAAA,GAA/DF,YAAY;AAElB,MAAMG,GAAG,gBAAG3E,IAAI,CAAA4E,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,kBAAkB,CAAC,CAAC;AAACC,IAAA,GAA7CF,GAAG;AACT,MAAMG,mBAAmB,gBAAG9E,IAAI,CAAA+E,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC;AAACC,IAAA,GAAzEF,mBAAmB;AACzB,MAAMG,kBAAkB,gBAAGjF,IAAI,CAAAkF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,gDAAgD,CAAC,CAAC;AAACC,IAAA,GAA1FF,kBAAkB;AACxB,MAAMG,UAAU,gBAAGpF,IAAI,CAAAqF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,IAAA,GAAvDF,UAAU;AAChB,MAAMG,SAAS,gBAAGvF,IAAI,CAAAwF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAACC,IAAA,GAAzDF,SAAS;AACf,MAAMG,WAAW,gBAAG1F,IAAI,CAAA2F,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC;;AAEzE;AAAAC,IAAA,GAFMF,WAAW;AAGjBG,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;EAC1C,IAAIA,KAAK,CAACC,OAAO,KACfD,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,0CAA0C,CAAC,IAClEF,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,gCAAgC,CAAC,IACxDF,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,gCAAgC,CAAC,CACzD,EAAE;IACDC,OAAO,CAACC,IAAI,CAAC,qCAAqC,EAAEJ,KAAK,CAACC,OAAO,CAAC;IAClED,KAAK,CAACK,cAAc,CAAC,CAAC;IACtB,OAAO,KAAK;EACd;AACF,CAAC,CAAC;;AAEF;AACAP,MAAM,CAACC,gBAAgB,CAAC,oBAAoB,EAAGC,KAAK,IAAK;EACvD,IAAIA,KAAK,CAACM,MAAM,IAAIN,KAAK,CAACM,MAAM,CAACL,OAAO,KACtCD,KAAK,CAACM,MAAM,CAACL,OAAO,CAACC,QAAQ,CAAC,0CAA0C,CAAC,IACzEF,KAAK,CAACM,MAAM,CAACL,OAAO,CAACC,QAAQ,CAAC,gCAAgC,CAAC,IAC/DF,KAAK,CAACM,MAAM,CAACL,OAAO,CAACC,QAAQ,CAAC,gCAAgC,CAAC,CAChE,EAAE;IACDC,OAAO,CAACC,IAAI,CAAC,iDAAiD,EAAEJ,KAAK,CAACM,MAAM,CAACL,OAAO,CAAC;IACrFD,KAAK,CAACK,cAAc,CAAC,CAAC;EACxB;AACF,CAAC,CAAC;AACF;AACA,MAAME,UAAU,GAAGA,CAAA,kBACjBxF,OAAA;EAAKyF,SAAS,EAAC,4FAA4F;EAAAC,QAAA,eACzG1F,OAAA;IAAKyF,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1B1F,OAAA;MAAKyF,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvB1F,OAAA;QAAKyF,SAAS,EAAC;MAA6E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACnG9F,OAAA;QAAKyF,SAAS,EAAC;MAA0F;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7G,CAAC,eACN9F,OAAA;MAAGyF,SAAS,EAAC,2BAA2B;MAAAC,QAAA,EAAC;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAC5D9F,OAAA;MAAGyF,SAAS,EAAC,4BAA4B;MAAAC,QAAA,EAAC;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/D;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACC,IAAA,GAXIP,UAAU;AAahB,SAASQ,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM;IAAEC;EAAQ,CAAC,GAAG1G,WAAW,CAAE2G,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EACxD,oBACEpG,OAAA,CAACN,aAAa;IAAAgG,QAAA,eACZ1F,OAAA,CAACP,aAAa;MAAAiG,QAAA,GACXQ,OAAO,iBAAIlG,OAAA,CAACT,MAAM;QAAAoG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtB9F,OAAA,CAACb,aAAa;QAAAuG,QAAA,eACd1F,OAAA,CAACZ,MAAM;UAAAsG,QAAA,gBAEL1F,OAAA,CAACX,KAAK;YAACgH,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEtG,OAAA,CAACJ,KAAK;cAAA+F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3C9F,OAAA,CAACX,KAAK;YAACgH,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEtG,OAAA,CAACH,QAAQ;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjD9F,OAAA,CAACX,KAAK;YAACgH,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEtG,OAAA,CAACF,IAAI;cAAA6F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrC9F,OAAA,CAACX,KAAK;YAACgH,IAAI,EAAC,eAAe;YAACC,OAAO,eAAEtG,OAAA,CAAC4E,WAAW;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGxD9F,OAAA,CAACX,KAAK;YAACgH,IAAI,EAAC,QAAQ;YAACC,OAAO,eAC1BtG,OAAA,CAACf,QAAQ;cAACsH,QAAQ,eAAEvG,OAAA,CAACwF,UAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,eACjC1F,OAAA,CAACsB,SAAS;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEJ9F,OAAA,CAACX,KAAK;YAACgH,IAAI,EAAC,OAAO;YAACC,OAAO,eACzBtG,OAAA,CAACf,QAAQ;cAACsH,QAAQ,eAAEvG,OAAA,CAACwF,UAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,eACjC1F,OAAA,CAACuD,IAAI;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACJ9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,QAAQ;YACbC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACf,QAAQ;gBAACsH,QAAQ,eAAEvG,OAAA,CAACwF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjC1F,OAAA,CAACoD,KAAK;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,UAAU;YACfC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACf,QAAQ;gBAACsH,QAAQ,eAAEvG,OAAA,CAACwF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjC1F,OAAA,CAAC8C,OAAO;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,eAAe;YACpBC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACf,QAAQ;gBAACsH,QAAQ,eAAEvG,OAAA,CAACwF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjC1F,OAAA,CAAC8C,OAAO;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,eAAe;YACpBC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACf,QAAQ;gBAACsH,QAAQ,eAAEvG,OAAA,CAACwF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjC1F,OAAA,CAAC0D,YAAY;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,oBAAoB;YACzBC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACf,QAAQ;gBAACsH,QAAQ,eAAEvG,OAAA,CAACwF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjC1F,OAAA,CAAC0D,YAAY;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,WAAW;YAChBC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACf,QAAQ;gBAACsH,QAAQ,eAAEvG,OAAA,CAACwF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjC1F,OAAA,CAAC6D,GAAG;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,YAAY;YACjBC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACf,QAAQ;gBAACsH,QAAQ,eAAEvG,OAAA,CAACwF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjC1F,OAAA,CAACC,IAAI;kBAAA0F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,sBAAsB;YAC3BC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACf,QAAQ;gBAACsH,QAAQ,eAAEvG,OAAA,CAACwF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjC1F,OAAA,CAACyB,SAAS;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,kBAAkB;YACvBC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACf,QAAQ;gBAACsH,QAAQ,eAAEvG,OAAA,CAACwF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjC1F,OAAA,CAACO,UAAU;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAIF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACI,QAAQ;gBAAAuF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,eAAe;YACpBC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACf,QAAQ;gBAACsH,QAAQ,eAAEvG,OAAA,CAACwF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjC1F,OAAA,CAAC4B,WAAW;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,sBAAsB;YAC3BC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACf,QAAQ;gBAACsH,QAAQ,eAAEvG,OAAA,CAACwF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjC1F,OAAA,CAACkC,aAAa;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,qBAAqB;YAC1BC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACf,QAAQ;gBAACsH,QAAQ,eAAEvG,OAAA,CAACwF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjC1F,OAAA,CAACqC,YAAY;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,eAAe;YACpBC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACf,QAAQ;gBAACsH,QAAQ,eAAEvG,OAAA,CAACwF,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjC1F,OAAA,CAAC2C,oBAAoB;kBAAA+C,QAAA,eACnB1F,OAAA,CAACwC,OAAO;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACiD,OAAO;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,kBAAkB;YACvBC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACL,mBAAmB;gBAAA+F,QAAA,eAClB1F,OAAA,CAACmB,cAAc;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,cAAc;YACnBC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACL,mBAAmB;gBAAA+F,QAAA,eAClB1F,OAAA,CAACgB,KAAK;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,cAAc;YACnBC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACL,mBAAmB;gBAAA+F,QAAA,eAClB1F,OAAA,CAACU,KAAK;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,kBAAkB;YACvBC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACL,mBAAmB;gBAAA+F,QAAA,eAClB1F,OAAA,CAACa,WAAW;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,uBAAuB;YAC5BC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACL,mBAAmB;gBAAA+F,QAAA,eAClB1F,OAAA,CAACa,WAAW;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,wBAAwB;YAC7BC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACL,mBAAmB;gBAAA+F,QAAA,eAClB1F,OAAA,CAACgE,mBAAmB;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,gBAAgB;YACrBC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACL,mBAAmB;gBAAA+F,QAAA,eAClB1F,OAAA,CAAC+B,YAAY;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,sBAAsB;YAC3BC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACL,mBAAmB;gBAAA+F,QAAA,eAClB1F,OAAA,CAACmE,kBAAkB;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,cAAc;YACnBC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACL,mBAAmB;gBAAA+F,QAAA,eAClB1F,OAAA,CAACsE,UAAU;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEF9F,OAAA,CAACX,KAAK;YACJgH,IAAI,EAAC,cAAc;YACnBC,OAAO,eACLtG,OAAA,CAACV,cAAc;cAAAoG,QAAA,eACb1F,OAAA,CAACL,mBAAmB;gBAAA+F,QAAA,eAClB1F,OAAA,CAACyE,SAAS;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEpB;AAACG,EAAA,CAxSQD,GAAG;EAAA,QACUxG,WAAW;AAAA;AAAAgH,IAAA,GADxBR,GAAG;AA0SZ,eAAeA,GAAG;AAAC,IAAA9F,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAiB,IAAA,EAAAS,IAAA;AAAAC,YAAA,CAAAvG,EAAA;AAAAuG,YAAA,CAAAtG,GAAA;AAAAsG,YAAA,CAAApG,GAAA;AAAAoG,YAAA,CAAAnG,GAAA;AAAAmG,YAAA,CAAAjG,GAAA;AAAAiG,YAAA,CAAAhG,GAAA;AAAAgG,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAA7F,GAAA;AAAA6F,YAAA,CAAA3F,GAAA;AAAA2F,YAAA,CAAA1F,IAAA;AAAA0F,YAAA,CAAAxF,IAAA;AAAAwF,YAAA,CAAAvF,IAAA;AAAAuF,YAAA,CAAArF,IAAA;AAAAqF,YAAA,CAAApF,IAAA;AAAAoF,YAAA,CAAAlF,IAAA;AAAAkF,YAAA,CAAAjF,IAAA;AAAAiF,YAAA,CAAA/E,IAAA;AAAA+E,YAAA,CAAA9E,IAAA;AAAA8E,YAAA,CAAA5E,IAAA;AAAA4E,YAAA,CAAA3E,IAAA;AAAA2E,YAAA,CAAAzE,IAAA;AAAAyE,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAAtE,IAAA;AAAAsE,YAAA,CAAArE,IAAA;AAAAqE,YAAA,CAAAnE,IAAA;AAAAmE,YAAA,CAAAlE,IAAA;AAAAkE,YAAA,CAAAhE,IAAA;AAAAgE,YAAA,CAAA/D,IAAA;AAAA+D,YAAA,CAAA7D,IAAA;AAAA6D,YAAA,CAAA5D,IAAA;AAAA4D,YAAA,CAAA1D,IAAA;AAAA0D,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAAvD,IAAA;AAAAuD,YAAA,CAAAtD,IAAA;AAAAsD,YAAA,CAAApD,IAAA;AAAAoD,YAAA,CAAAnD,IAAA;AAAAmD,YAAA,CAAAjD,IAAA;AAAAiD,YAAA,CAAAhD,IAAA;AAAAgD,YAAA,CAAA9C,IAAA;AAAA8C,YAAA,CAAA7C,IAAA;AAAA6C,YAAA,CAAA3C,IAAA;AAAA2C,YAAA,CAAA1C,IAAA;AAAA0C,YAAA,CAAAxC,IAAA;AAAAwC,YAAA,CAAAvC,IAAA;AAAAuC,YAAA,CAAArC,IAAA;AAAAqC,YAAA,CAAApC,IAAA;AAAAoC,YAAA,CAAAlC,IAAA;AAAAkC,YAAA,CAAAjC,IAAA;AAAAiC,YAAA,CAAA/B,IAAA;AAAA+B,YAAA,CAAA9B,IAAA;AAAA8B,YAAA,CAAA5B,IAAA;AAAA4B,YAAA,CAAA3B,IAAA;AAAA2B,YAAA,CAAAV,IAAA;AAAAU,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}