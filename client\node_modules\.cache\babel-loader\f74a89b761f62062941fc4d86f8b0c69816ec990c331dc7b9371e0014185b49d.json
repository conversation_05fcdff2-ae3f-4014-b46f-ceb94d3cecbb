{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { FaPlayCircle, FaVideo, FaGraduationCap, FaDownload, FaEye, FaTimes, FaChevronDown, FaSearch, FaExpand, FaCompress } from \"react-icons/fa\";\nimport { TbVideo, TbSchool, <PERSON>b<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ortAscending, Tb<PERSON><PERSON>, TbD<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>alenda<PERSON>, <PERSON>b<PERSON><PERSON>, TbChevronDown as TbChevronDownIcon, TbChevronUp, TbX, TbAlertTriangle, TbInfoCircle, TbCheck, TbSubtitles } from \"react-icons/tb\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction VideoLessons() {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const dispatch = useDispatch();\n\n  // State management\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedLevel, setSelectedLevel] = useState((user === null || user === void 0 ? void 0 : user.level) || \"primary\");\n  const [selectedClass, setSelectedClass] = useState((user === null || user === void 0 ? void 0 : user.className) || \"1\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Video player state\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [videoError, setVideoError] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n\n  // Available classes based on level\n  const availableClasses = useMemo(() => {\n    if (selectedLevel === \"primary\") return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    if (selectedLevel === \"secondary\") return [\"1\", \"2\", \"3\", \"4\"];\n    if (selectedLevel === \"advance\") return [\"5\", \"6\"];\n    return [];\n  }, [selectedLevel]);\n\n  // Available subjects based on level\n  const availableSubjects = useMemo(() => {\n    if (selectedLevel === \"primary\") return primarySubjects;\n    if (selectedLevel === \"secondary\") return secondarySubjects;\n    if (selectedLevel === \"advance\") return advanceSubjects;\n    return [];\n  }, [selectedLevel]);\n\n  // Fetch videos\n  const fetchVideos = useCallback(async () => {\n    try {\n      var _response$data;\n      setLoading(true);\n      setError(null);\n      dispatch(ShowLoading());\n      const filters = {\n        level: selectedLevel,\n        className: selectedClass,\n        subject: selectedSubject === \"all\" ? \"\" : selectedSubject,\n        content: \"videos\"\n      };\n      const response = await getStudyMaterial(filters);\n      if (response !== null && response !== void 0 && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.success) {\n        setVideos(response.data.data || []);\n      } else {\n        var _response$data2;\n        setError((response === null || response === void 0 ? void 0 : (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message) || \"Failed to fetch videos\");\n        setVideos([]);\n      }\n    } catch (error) {\n      console.error(\"Error fetching videos:\", error);\n      setError(\"Failed to load videos. Please try again.\");\n      setVideos([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [selectedLevel, selectedClass, selectedSubject, dispatch]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos;\n\n    // Apply search filter\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(video => {\n        var _video$title, _video$subject;\n        return ((_video$title = video.title) === null || _video$title === void 0 ? void 0 : _video$title.toLowerCase().includes(searchLower)) || ((_video$subject = video.subject) === null || _video$subject === void 0 ? void 0 : _video$subject.toLowerCase().includes(searchLower));\n      });\n    }\n\n    // Apply sorting\n    const sorted = [...filtered].sort((a, b) => {\n      switch (sortBy) {\n        case \"newest\":\n          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);\n        case \"oldest\":\n          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);\n        case \"title\":\n          return (a.title || \"\").localeCompare(b.title || \"\");\n        case \"subject\":\n          return (a.subject || \"\").localeCompare(b.subject || \"\");\n        default:\n          return 0;\n      }\n    });\n    return sorted;\n  }, [videos, searchTerm, sortBy]);\n\n  // Video handlers\n  const handleShowVideo = async index => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video !== null && video !== void 0 && video.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    if (videoRef) {\n      videoRef.pause();\n    }\n  };\n  const toggleVideoExpansion = () => {\n    setIsVideoExpanded(!isVideoExpanded);\n  };\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async videoUrl => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          credentials: 'include'\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.error('❌ Error getting signed URL:', error);\n        return videoUrl;\n      }\n    }\n    return videoUrl;\n  };\n\n  // Get thumbnail URL\n  const getThumbnailUrl = video => {\n    if (video.thumbnail) {\n      return video.thumbnail;\n    }\n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      let videoId = video.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    return '/api/placeholder/400/225';\n  };\n\n  // Effects\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.level && user !== null && user !== void 0 && user.className) {\n      setSelectedLevel(user.level);\n      setSelectedClass(user.className);\n    }\n  }, [user]);\n\n  // Clear search and refresh\n  const handleClearSearch = () => {\n    setSearchTerm(\"\");\n  };\n  const handleRefresh = () => {\n    setSearchTerm(\"\");\n    setSelectedSubject(\"all\");\n    fetchVideos();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"study-material-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"study-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"study-header-content\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"study-title-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"study-icon-wrapper\",\n            children: /*#__PURE__*/_jsxDEV(TbVideo, {\n              className: \"study-main-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"study-title-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"study-title\",\n              children: \"Video Lessons\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"study-subtitle\",\n              children: \"Watch educational videos to enhance your learning\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-modern py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"study-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"study-filters\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"filter-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbSchool, {\n                className: \"filter-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), \"Level\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedLevel,\n              onChange: e => setSelectedLevel(e.target.value),\n              className: \"filter-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"primary\",\n                children: \"Primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"secondary\",\n                children: \"Secondary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"advance\",\n                children: \"Advance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"filter-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbSchool, {\n                className: \"filter-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this), \"Class\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedClass,\n              onChange: e => setSelectedClass(e.target.value),\n              className: \"filter-select\",\n              children: availableClasses.map(cls => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: cls,\n                children: cls\n              }, cls, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"filter-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbFilter, {\n                className: \"filter-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 17\n              }, this), \"Subject\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedSubject,\n              onChange: e => setSelectedSubject(e.target.value),\n              className: \"filter-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Subjects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 17\n              }, this), availableSubjects.map(subject => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: subject,\n                children: subject\n              }, subject, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"study-search-sort\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-container\",\n            children: [/*#__PURE__*/_jsxDEV(TbSearch, {\n              className: \"search-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search videos by title or subject...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              className: \"search-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleClearSearch,\n              className: \"clear-search-btn\",\n              children: /*#__PURE__*/_jsxDEV(TbX, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sort-container\",\n            children: [/*#__PURE__*/_jsxDEV(TbSortAscending, {\n              className: \"sort-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: sortBy,\n              onChange: e => setSortBy(e.target.value),\n              className: \"sort-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"newest\",\n                children: \"Newest First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"oldest\",\n                children: \"Oldest First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"title\",\n                children: \"Title A-Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"subject\",\n                children: \"Subject A-Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRefresh,\n            className: \"refresh-btn\",\n            children: [/*#__PURE__*/_jsxDEV(TbDownload, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this), \"Refresh\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading videos...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-state\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n          className: \"error-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Error Loading Videos\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchVideos,\n          className: \"retry-btn\",\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 11\n      }, this) : filteredAndSortedVideos.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n        children: filteredAndSortedVideos.map((video, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"study-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"study-card-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"study-card-meta\",\n              children: [/*#__PURE__*/_jsxDEV(FaVideo, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Video\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"study-card-title\",\n              children: video.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"study-card-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"study-card-subject\",\n                children: video.subject\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"study-card-class\",\n                children: [\"Class \", video.className]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 17\n          }, this), (video.videoUrl || video.videoID) && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-thumbnail-container\",\n            onClick: () => handleShowVideo(index),\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: getThumbnailUrl(video),\n              alt: video.title,\n              className: \"video-thumbnail\",\n              onError: e => {\n                // Fallback logic for failed thumbnails\n                if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                  // For YouTube videos, try different quality thumbnails\n                  let videoId = video.videoID;\n                  if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                    const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                    videoId = match ? match[1] : videoId;\n                  }\n                  const fallbacks = [`https://img.youtube.com/vi/${videoId}/hqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/default.jpg`, '/api/placeholder/400/225'];\n                  const currentSrc = e.target.src;\n                  const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                  if (currentIndex < fallbacks.length - 1) {\n                    e.target.src = fallbacks[currentIndex + 1];\n                  }\n                } else {\n                  e.target.src = '/api/placeholder/400/225';\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-play-overlay\",\n              children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                className: \"play-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-duration-badge\",\n              children: video.duration || \"Video\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"study-card-actions\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"action-btn primary\",\n              onClick: () => handleShowVideo(index),\n              children: [/*#__PURE__*/_jsxDEV(TbPlay, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 21\n              }, this), \"Watch Video\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n          className: \"empty-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No Videos Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No video lessons are available for your current selection.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"suggestion\",\n          children: \"Try selecting a different class or subject.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this), showVideoIndices.length > 0 && currentVideoIndex !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `video-overlay ${isVideoExpanded ? 'expanded' : ''}`,\n      onClick: e => {\n        if (e.target === e.currentTarget) handleHideVideo();\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `video-modal ${isVideoExpanded ? 'expanded' : ''}`,\n        children: (() => {\n          const video = filteredAndSortedVideos[currentVideoIndex];\n          if (!video) return /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Video not found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 34\n          }, this);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"video-title\",\n                  children: video.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-subject\",\n                    children: video.subject\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-class\",\n                    children: [\"Class \", video.className]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 491,\n                    columnNumber: 25\n                  }, this), video.level && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-level\",\n                    children: video.level\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 492,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-controls\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"control-btn expand-btn\",\n                  onClick: toggleVideoExpansion,\n                  title: isVideoExpanded ? \"Exit fullscreen\" : \"Enter fullscreen\",\n                  children: isVideoExpanded ? /*#__PURE__*/_jsxDEV(FaCompress, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 44\n                  }, this) : /*#__PURE__*/_jsxDEV(FaExpand, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 61\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"control-btn close-btn\",\n                  onClick: handleHideVideo,\n                  title: \"Close video\",\n                  children: /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-container\",\n              children: video.videoUrl ? /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '15px',\n                  background: '#000',\n                  borderRadius: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"video\", {\n                  ref: ref => setVideoRef(ref),\n                  controls: true,\n                  autoPlay: true,\n                  playsInline: true,\n                  preload: \"metadata\",\n                  width: \"100%\",\n                  height: \"400\",\n                  poster: getThumbnailUrl(video),\n                  style: {\n                    width: '100%',\n                    height: '400px',\n                    backgroundColor: '#000'\n                  },\n                  onError: e => {\n                    setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                  },\n                  onCanPlay: () => {\n                    setVideoError(null);\n                  },\n                  onLoadStart: () => {\n                    console.log('🎬 Video loading started');\n                  },\n                  crossOrigin: \"anonymous\",\n                  children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                    src: video.signedVideoUrl || video.videoUrl,\n                    type: \"video/mp4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 541,\n                    columnNumber: 29\n                  }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => /*#__PURE__*/_jsxDEV(\"track\", {\n                    kind: \"subtitles\",\n                    src: subtitle.url,\n                    srcLang: subtitle.language,\n                    label: subtitle.languageName,\n                    default: subtitle.isDefault || index === 0\n                  }, `${subtitle.language}-${index}`, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 545,\n                    columnNumber: 31\n                  }, this)), \"Your browser does not support the video tag.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 27\n                }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"subtitle-indicator\",\n                  children: [/*#__PURE__*/_jsxDEV(TbSubtitles, {\n                    className: \"subtitle-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 561,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Subtitles available in \", video.subtitles.length, \" language(s)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 562,\n                    columnNumber: 31\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 29\n                }, this), videoError && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-error-overlay\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"error-content\",\n                    children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n                      className: \"error-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 570,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: videoError\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 571,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setVideoError(null),\n                      className: \"dismiss-error-btn\",\n                      children: \"Dismiss\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 572,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 569,\n                    columnNumber: 31\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 568,\n                  columnNumber: 29\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 23\n              }, this) : video.videoID ?\n              /*#__PURE__*/\n              // Fallback to YouTube embed if no videoUrl\n              _jsxDEV(\"iframe\", {\n                src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                title: video.title,\n                frameBorder: \"0\",\n                allowFullScreen: true,\n                className: \"video-iframe\",\n                onLoad: () => console.log('✅ YouTube iframe loaded')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-error\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"error-icon\",\n                  children: \"\\u26A0\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"Video Unavailable\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: videoError || \"This video cannot be played at the moment.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"error-actions\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: video.signedVideoUrl || video.videoUrl,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: \"external-link-btn\",\n                    children: \"\\uD83D\\uDCF1 Open in New Tab\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 595,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 594,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 17\n          }, this);\n        })()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 479,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 256,\n    columnNumber: 5\n  }, this);\n}\n_s(VideoLessons, \"qmMFcqvf++m5NqfxmcumJeLo0SI=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = VideoLessons;\nexport default VideoLessons;\nvar _c;\n$RefreshReg$(_c, \"VideoLessons\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "motion", "AnimatePresence", "getStudyMaterial", "useDispatch", "useSelector", "HideLoading", "ShowLoading", "FaPlayCircle", "FaVideo", "FaGraduationCap", "FaDownload", "FaEye", "FaTimes", "FaChevronDown", "FaSearch", "FaExpand", "FaCompress", "TbVideo", "TbSchool", "TbSearch", "Tb<PERSON><PERSON>er", "TbSortAscending", "TbPlay", "TbDownload", "TbEye", "TbCalendar", "TbUser", "TbChevronDown", "TbChevronDownIcon", "TbChevronUp", "TbX", "TbAlertTriangle", "TbInfoCircle", "TbCheck", "TbSubtitles", "primarySubjects", "secondarySubjects", "advanceSubjects", "jsxDEV", "_jsxDEV", "VideoLessons", "_s", "user", "state", "dispatch", "videos", "setVideos", "loading", "setLoading", "error", "setError", "selectedLevel", "setSelectedLevel", "level", "selectedClass", "setSelectedClass", "className", "selectedSubject", "setSelectedSubject", "searchTerm", "setSearchTerm", "sortBy", "setSortBy", "currentVideoIndex", "setCurrentVideoIndex", "showVideoIndices", "setShowVideoIndices", "isVideoExpanded", "setIsVideoExpanded", "videoError", "setVideoError", "videoRef", "setVideoRef", "availableClasses", "availableSubjects", "fetchVideos", "_response$data", "filters", "subject", "content", "response", "data", "success", "_response$data2", "message", "console", "filteredAndSortedVideos", "filtered", "trim", "searchLower", "toLowerCase", "filter", "video", "_video$title", "_video$subject", "title", "includes", "sorted", "sort", "a", "b", "Date", "createdAt", "localeCompare", "handleShowVideo", "index", "videoUrl", "signedUrl", "getSignedVideoUrl", "signedVideoUrl", "warn", "handleHideVideo", "pause", "toggleVideoExpansion", "fetch", "encodeURIComponent", "method", "headers", "credentials", "ok", "Error", "status", "json", "log", "getThumbnailUrl", "thumbnail", "videoID", "videoId", "match", "handleClearSearch", "handleRefresh", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "e", "target", "map", "cls", "type", "placeholder", "onClick", "length", "src", "alt", "onError", "fallbacks", "currentSrc", "currentIndex", "findIndex", "url", "split", "pop", "duration", "currentTarget", "style", "padding", "background", "borderRadius", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "poster", "backgroundColor", "onCanPlay", "onLoadStart", "crossOrigin", "subtitles", "subtitle", "kind", "srcLang", "language", "label", "languageName", "default", "isDefault", "frameBorder", "allowFullScreen", "onLoad", "href", "rel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/index.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\n\nimport {\n  FaPlayCircle,\n  FaVideo,\n  FaGraduationCap,\n  FaDownload,\n  FaEye,\n  FaTimes,\n  FaChevronDown,\n  FaSearch,\n  FaExpand,\n  FaCompress,\n} from \"react-icons/fa\";\nimport {\n  TbVideo,\n  TbSchool,\n  TbSearch,\n  TbFilter,\n  TbSortAscending,\n  TbPlay,\n  TbDownload,\n  TbEye,\n  TbCalendar,\n  TbUser,\n  TbChevronDown as TbChevronDownIcon,\n  TbChevronUp,\n  TbX,\n  Tb<PERSON>lertTriangle,\n  TbIn<PERSON><PERSON><PERSON><PERSON>,\n  Tb<PERSON><PERSON><PERSON>,\n  TbSubtitles,\n} from \"react-icons/tb\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\n\nfunction VideoLessons() {\n  const { user } = useSelector((state) => state.user);\n  const dispatch = useDispatch();\n\n  // State management\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedLevel, setSelectedLevel] = useState(user?.level || \"primary\");\n  const [selectedClass, setSelectedClass] = useState(user?.className || \"1\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Video player state\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [videoError, setVideoError] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n\n  // Available classes based on level\n  const availableClasses = useMemo(() => {\n    if (selectedLevel === \"primary\") return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    if (selectedLevel === \"secondary\") return [\"1\", \"2\", \"3\", \"4\"];\n    if (selectedLevel === \"advance\") return [\"5\", \"6\"];\n    return [];\n  }, [selectedLevel]);\n\n  // Available subjects based on level\n  const availableSubjects = useMemo(() => {\n    if (selectedLevel === \"primary\") return primarySubjects;\n    if (selectedLevel === \"secondary\") return secondarySubjects;\n    if (selectedLevel === \"advance\") return advanceSubjects;\n    return [];\n  }, [selectedLevel]);\n\n  // Fetch videos\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      dispatch(ShowLoading());\n\n      const filters = {\n        level: selectedLevel,\n        className: selectedClass,\n        subject: selectedSubject === \"all\" ? \"\" : selectedSubject,\n        content: \"videos\"\n      };\n\n      const response = await getStudyMaterial(filters);\n      \n      if (response?.data?.success) {\n        setVideos(response.data.data || []);\n      } else {\n        setError(response?.data?.message || \"Failed to fetch videos\");\n        setVideos([]);\n      }\n    } catch (error) {\n      console.error(\"Error fetching videos:\", error);\n      setError(\"Failed to load videos. Please try again.\");\n      setVideos([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [selectedLevel, selectedClass, selectedSubject, dispatch]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos;\n\n    // Apply search filter\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(video =>\n        video.title?.toLowerCase().includes(searchLower) ||\n        video.subject?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Apply sorting\n    const sorted = [...filtered].sort((a, b) => {\n      switch (sortBy) {\n        case \"newest\":\n          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);\n        case \"oldest\":\n          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);\n        case \"title\":\n          return (a.title || \"\").localeCompare(b.title || \"\");\n        case \"subject\":\n          return (a.subject || \"\").localeCompare(b.subject || \"\");\n        default:\n          return 0;\n      }\n    });\n\n    return sorted;\n  }, [videos, searchTerm, sortBy]);\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedVideos[index];\n\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    if (videoRef) {\n      videoRef.pause();\n    }\n  };\n\n  const toggleVideoExpansion = () => {\n    setIsVideoExpanded(!isVideoExpanded);\n  };\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async (videoUrl) => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          credentials: 'include'\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.error('❌ Error getting signed URL:', error);\n        return videoUrl;\n      }\n    }\n\n    return videoUrl;\n  };\n\n  // Get thumbnail URL\n  const getThumbnailUrl = (video) => {\n    if (video.thumbnail) {\n      return video.thumbnail;\n    }\n    \n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      let videoId = video.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    \n    return '/api/placeholder/400/225';\n  };\n\n  // Effects\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  useEffect(() => {\n    if (user?.level && user?.className) {\n      setSelectedLevel(user.level);\n      setSelectedClass(user.className);\n    }\n  }, [user]);\n\n  // Clear search and refresh\n  const handleClearSearch = () => {\n    setSearchTerm(\"\");\n  };\n\n  const handleRefresh = () => {\n    setSearchTerm(\"\");\n    setSelectedSubject(\"all\");\n    fetchVideos();\n  };\n\n  return (\n    <div className=\"study-material-container\">\n      <div className=\"study-header\">\n        <div className=\"study-header-content\">\n          <div className=\"study-title-section\">\n            <div className=\"study-icon-wrapper\">\n              <TbVideo className=\"study-main-icon\" />\n            </div>\n            <div className=\"study-title-content\">\n              <h1 className=\"study-title\">Video Lessons</h1>\n              <p className=\"study-subtitle\">\n                Watch educational videos to enhance your learning\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"container-modern py-8\">\n        {/* Filters and Controls */}\n        <div className=\"study-controls\">\n          <div className=\"study-filters\">\n            {/* Level Filter */}\n            <div className=\"filter-group\">\n              <label className=\"filter-label\">\n                <TbSchool className=\"filter-icon\" />\n                Level\n              </label>\n              <select\n                value={selectedLevel}\n                onChange={(e) => setSelectedLevel(e.target.value)}\n                className=\"filter-select\"\n              >\n                <option value=\"primary\">Primary</option>\n                <option value=\"secondary\">Secondary</option>\n                <option value=\"advance\">Advance</option>\n              </select>\n            </div>\n\n            {/* Class Filter */}\n            <div className=\"filter-group\">\n              <label className=\"filter-label\">\n                <TbSchool className=\"filter-icon\" />\n                Class\n              </label>\n              <select\n                value={selectedClass}\n                onChange={(e) => setSelectedClass(e.target.value)}\n                className=\"filter-select\"\n              >\n                {availableClasses.map((cls) => (\n                  <option key={cls} value={cls}>\n                    {cls}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Subject Filter */}\n            <div className=\"filter-group\">\n              <label className=\"filter-label\">\n                <TbFilter className=\"filter-icon\" />\n                Subject\n              </label>\n              <select\n                value={selectedSubject}\n                onChange={(e) => setSelectedSubject(e.target.value)}\n                className=\"filter-select\"\n              >\n                <option value=\"all\">All Subjects</option>\n                {availableSubjects.map((subject) => (\n                  <option key={subject} value={subject}>\n                    {subject}\n                  </option>\n                ))}\n              </select>\n            </div>\n          </div>\n\n          {/* Search and Sort */}\n          <div className=\"study-search-sort\">\n            <div className=\"search-container\">\n              <TbSearch className=\"search-icon\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search videos by title or subject...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"search-input\"\n              />\n              {searchTerm && (\n                <button onClick={handleClearSearch} className=\"clear-search-btn\">\n                  <TbX />\n                </button>\n              )}\n            </div>\n\n            <div className=\"sort-container\">\n              <TbSortAscending className=\"sort-icon\" />\n              <select\n                value={sortBy}\n                onChange={(e) => setSortBy(e.target.value)}\n                className=\"sort-select\"\n              >\n                <option value=\"newest\">Newest First</option>\n                <option value=\"oldest\">Oldest First</option>\n                <option value=\"title\">Title A-Z</option>\n                <option value=\"subject\">Subject A-Z</option>\n              </select>\n            </div>\n\n            <button onClick={handleRefresh} className=\"refresh-btn\">\n              <TbDownload />\n              Refresh\n            </button>\n          </div>\n        </div>\n\n        {/* Loading State */}\n        {loading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>Loading videos...</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <TbAlertTriangle className=\"error-icon\" />\n            <h3>Error Loading Videos</h3>\n            <p>{error}</p>\n            <button onClick={fetchVideos} className=\"retry-btn\">\n              Try Again\n            </button>\n          </div>\n        ) : filteredAndSortedVideos.length > 0 ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {filteredAndSortedVideos.map((video, index) => (\n              <div key={index} className=\"study-card\">\n                <div className=\"study-card-header\">\n                  <div className=\"study-card-meta\">\n                    <FaVideo />\n                    <span>Video</span>\n                  </div>\n\n                  <div className=\"study-card-title\">\n                    {video.title}\n                  </div>\n\n                  <div className=\"study-card-info\">\n                    <span className=\"study-card-subject\">{video.subject}</span>\n                    <span className=\"study-card-class\">Class {video.className}</span>\n                  </div>\n                </div>\n\n                {/* Video Thumbnail */}\n                {(video.videoUrl || video.videoID) && (\n                  <div className=\"video-thumbnail-container\" onClick={() => handleShowVideo(index)}>\n                    <img\n                      src={getThumbnailUrl(video)}\n                      alt={video.title}\n                      className=\"video-thumbnail\"\n                      onError={(e) => {\n                        // Fallback logic for failed thumbnails\n                        if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                          // For YouTube videos, try different quality thumbnails\n                          let videoId = video.videoID;\n                          if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                            const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                            videoId = match ? match[1] : videoId;\n                          }\n\n                          const fallbacks = [\n                            `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n                            `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,\n                            `https://img.youtube.com/vi/${videoId}/default.jpg`,\n                            '/api/placeholder/400/225'\n                          ];\n\n                          const currentSrc = e.target.src;\n                          const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n\n                          if (currentIndex < fallbacks.length - 1) {\n                            e.target.src = fallbacks[currentIndex + 1];\n                          }\n                        } else {\n                          e.target.src = '/api/placeholder/400/225';\n                        }\n                      }}\n                    />\n                    <div className=\"video-play-overlay\">\n                      <FaPlayCircle className=\"play-icon\" />\n                    </div>\n                    <div className=\"video-duration-badge\">\n                      {video.duration || \"Video\"}\n                    </div>\n                  </div>\n                )}\n\n                <div className=\"study-card-actions\">\n                  <button\n                    className=\"action-btn primary\"\n                    onClick={() => handleShowVideo(index)}\n                  >\n                    <TbPlay />\n                    Watch Video\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>No Videos Found</h3>\n            <p>No video lessons are available for your current selection.</p>\n            <p className=\"suggestion\">Try selecting a different class or subject.</p>\n          </div>\n        )}\n      </div>\n\n      {/* Enhanced Video Display */}\n      {showVideoIndices.length > 0 && currentVideoIndex !== null && (\n        <div className={`video-overlay ${isVideoExpanded ? 'expanded' : ''}`} onClick={(e) => {\n          if (e.target === e.currentTarget) handleHideVideo();\n        }}>\n          <div className={`video-modal ${isVideoExpanded ? 'expanded' : ''}`}>\n            {(() => {\n              const video = filteredAndSortedVideos[currentVideoIndex];\n              if (!video) return <div>Video not found</div>;\n\n              return (\n                <div className=\"video-content\">\n                  <div className=\"video-header\">\n                    <div className=\"video-info\">\n                      <h3 className=\"video-title\">{video.title}</h3>\n                      <div className=\"video-meta\">\n                        <span className=\"video-subject\">{video.subject}</span>\n                        <span className=\"video-class\">Class {video.className}</span>\n                        {video.level && <span className=\"video-level\">{video.level}</span>}\n                      </div>\n                    </div>\n                    <div className=\"video-controls\">\n                      <button\n                        className=\"control-btn expand-btn\"\n                        onClick={toggleVideoExpansion}\n                        title={isVideoExpanded ? \"Exit fullscreen\" : \"Enter fullscreen\"}\n                      >\n                        {isVideoExpanded ? <FaCompress /> : <FaExpand />}\n                      </button>\n                      <button\n                        className=\"control-btn close-btn\"\n                        onClick={handleHideVideo}\n                        title=\"Close video\"\n                      >\n                        <FaTimes />\n                      </button>\n                    </div>\n                  </div>\n                  <div className=\"video-container\">\n                    {video.videoUrl ? (\n                      <div style={{ padding: '15px', background: '#000', borderRadius: '8px' }}>\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"400\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '400px',\n                              backgroundColor: '#000'\n                            }}\n                            onError={(e) => {\n                              setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                            }}\n                            onCanPlay={() => {\n                              setVideoError(null);\n                            }}\n                            onLoadStart={() => {\n                              console.log('🎬 Video loading started');\n                            }}\n                            crossOrigin=\"anonymous\"\n                          >\n                            {/* Use signed URL if available, otherwise use original URL */}\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n\n                            {/* Add subtitle tracks if available */}\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n\n                            Your browser does not support the video tag.\n                          </video>\n\n                          {/* Subtitle indicator */}\n                          {video.subtitles && video.subtitles.length > 0 && (\n                            <div className=\"subtitle-indicator\">\n                              <TbSubtitles className=\"subtitle-icon\" />\n                              <span>Subtitles available in {video.subtitles.length} language(s)</span>\n                            </div>\n                          )}\n\n                          {/* Video error display */}\n                          {videoError && (\n                            <div className=\"video-error-overlay\">\n                              <div className=\"error-content\">\n                                <TbAlertTriangle className=\"error-icon\" />\n                                <p>{videoError}</p>\n                                <button onClick={() => setVideoError(null)} className=\"dismiss-error-btn\">\n                                  Dismiss\n                                </button>\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                    ) : video.videoID ? (\n                      // Fallback to YouTube embed if no videoUrl\n                      <iframe\n                        src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                        title={video.title}\n                        frameBorder=\"0\"\n                        allowFullScreen\n                        className=\"video-iframe\"\n                        onLoad={() => console.log('✅ YouTube iframe loaded')}\n                      ></iframe>\n                    ) : (\n                      <div className=\"video-error\">\n                        <div className=\"error-icon\">⚠️</div>\n                        <h3>Video Unavailable</h3>\n                        <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                        <div className=\"error-actions\">\n                          <a\n                            href={video.signedVideoUrl || video.videoUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"external-link-btn\"\n                          >\n                            📱 Open in New Tab\n                          </a>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              );\n            })()}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default VideoLessons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,OAAO,aAAa;AACpB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AAErE,SACEC,YAAY,EACZC,OAAO,EACPC,eAAe,EACfC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,aAAa,EACbC,QAAQ,EACRC,QAAQ,EACRC,UAAU,QACL,gBAAgB;AACvB,SACEC,OAAO,EACPC,QAAQ,EACRC,QAAQ,EACRC,QAAQ,EACRC,eAAe,EACfC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,aAAa,IAAIC,iBAAiB,EAClCC,WAAW,EACXC,GAAG,EACHC,eAAe,EACfC,YAAY,EACZC,OAAO,EACPC,WAAW,QACN,gBAAgB;AACvB,SAASC,eAAe,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjG,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGtC,WAAW,CAAEuC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAME,QAAQ,GAAGzC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAAC0C,MAAM,EAAEC,SAAS,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqD,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACuD,aAAa,EAAEC,gBAAgB,CAAC,GAAGxD,QAAQ,CAAC,CAAA8C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,KAAK,KAAI,SAAS,CAAC;EAC5E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG3D,QAAQ,CAAC,CAAA8C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEc,SAAS,KAAI,GAAG,CAAC;EAC1E,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC+D,UAAU,EAAEC,aAAa,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiE,MAAM,EAAEC,SAAS,CAAC,GAAGlE,QAAQ,CAAC,QAAQ,CAAC;;EAE9C;EACA,MAAM,CAACmE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACqE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACuE,eAAe,EAAEC,kBAAkB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACyE,UAAU,EAAEC,aAAa,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC2E,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;;EAE9C;EACA,MAAM6E,gBAAgB,GAAG1E,OAAO,CAAC,MAAM;IACrC,IAAIoD,aAAa,KAAK,SAAS,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC3E,IAAIA,aAAa,KAAK,WAAW,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC9D,IAAIA,aAAa,KAAK,SAAS,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IAClD,OAAO,EAAE;EACX,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMuB,iBAAiB,GAAG3E,OAAO,CAAC,MAAM;IACtC,IAAIoD,aAAa,KAAK,SAAS,EAAE,OAAOhB,eAAe;IACvD,IAAIgB,aAAa,KAAK,WAAW,EAAE,OAAOf,iBAAiB;IAC3D,IAAIe,aAAa,KAAK,SAAS,EAAE,OAAOd,eAAe;IACvD,OAAO,EAAE;EACX,CAAC,EAAE,CAACc,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMwB,WAAW,GAAG7E,WAAW,CAAC,YAAY;IAC1C,IAAI;MAAA,IAAA8E,cAAA;MACF5B,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACdN,QAAQ,CAACtC,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAMuE,OAAO,GAAG;QACdxB,KAAK,EAAEF,aAAa;QACpBK,SAAS,EAAEF,aAAa;QACxBwB,OAAO,EAAErB,eAAe,KAAK,KAAK,GAAG,EAAE,GAAGA,eAAe;QACzDsB,OAAO,EAAE;MACX,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAM9E,gBAAgB,CAAC2E,OAAO,CAAC;MAEhD,IAAIG,QAAQ,aAARA,QAAQ,gBAAAJ,cAAA,GAARI,QAAQ,CAAEC,IAAI,cAAAL,cAAA,eAAdA,cAAA,CAAgBM,OAAO,EAAE;QAC3BpC,SAAS,CAACkC,QAAQ,CAACC,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MACrC,CAAC,MAAM;QAAA,IAAAE,eAAA;QACLjC,QAAQ,CAAC,CAAA8B,QAAQ,aAARA,QAAQ,wBAAAG,eAAA,GAARH,QAAQ,CAAEC,IAAI,cAAAE,eAAA,uBAAdA,eAAA,CAAgBC,OAAO,KAAI,wBAAwB,CAAC;QAC7DtC,SAAS,CAAC,EAAE,CAAC;MACf;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdoC,OAAO,CAACpC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CC,QAAQ,CAAC,0CAA0C,CAAC;MACpDJ,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;MACjBJ,QAAQ,CAACvC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAAC8C,aAAa,EAAEG,aAAa,EAAEG,eAAe,EAAEb,QAAQ,CAAC,CAAC;;EAE7D;EACA,MAAM0C,uBAAuB,GAAGvF,OAAO,CAAC,MAAM;IAC5C,IAAIwF,QAAQ,GAAG1C,MAAM;;IAErB;IACA,IAAIc,UAAU,CAAC6B,IAAI,CAAC,CAAC,EAAE;MACrB,MAAMC,WAAW,GAAG9B,UAAU,CAAC+B,WAAW,CAAC,CAAC;MAC5CH,QAAQ,GAAGA,QAAQ,CAACI,MAAM,CAACC,KAAK;QAAA,IAAAC,YAAA,EAAAC,cAAA;QAAA,OAC9B,EAAAD,YAAA,GAAAD,KAAK,CAACG,KAAK,cAAAF,YAAA,uBAAXA,YAAA,CAAaH,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAK,cAAA,GAChDF,KAAK,CAACd,OAAO,cAAAgB,cAAA,uBAAbA,cAAA,CAAeJ,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC;MAAA,CACpD,CAAC;IACH;;IAEA;IACA,MAAMQ,MAAM,GAAG,CAAC,GAAGV,QAAQ,CAAC,CAACW,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC1C,QAAQvC,MAAM;QACZ,KAAK,QAAQ;UACX,OAAO,IAAIwC,IAAI,CAACD,CAAC,CAACE,SAAS,IAAI,CAAC,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,IAAI,CAAC,CAAC;QAChE,KAAK,QAAQ;UACX,OAAO,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,IAAI,CAAC,CAAC,GAAG,IAAID,IAAI,CAACD,CAAC,CAACE,SAAS,IAAI,CAAC,CAAC;QAChE,KAAK,OAAO;UACV,OAAO,CAACH,CAAC,CAACJ,KAAK,IAAI,EAAE,EAAEQ,aAAa,CAACH,CAAC,CAACL,KAAK,IAAI,EAAE,CAAC;QACrD,KAAK,SAAS;UACZ,OAAO,CAACI,CAAC,CAACrB,OAAO,IAAI,EAAE,EAAEyB,aAAa,CAACH,CAAC,CAACtB,OAAO,IAAI,EAAE,CAAC;QACzD;UACE,OAAO,CAAC;MACZ;IACF,CAAC,CAAC;IAEF,OAAOmB,MAAM;EACf,CAAC,EAAE,CAACpD,MAAM,EAAEc,UAAU,EAAEE,MAAM,CAAC,CAAC;;EAEhC;EACA,MAAM2C,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,MAAMb,KAAK,GAAGN,uBAAuB,CAACmB,KAAK,CAAC;IAE5CzC,oBAAoB,CAACyC,KAAK,CAAC;IAC3BvC,mBAAmB,CAAC,CAACuC,KAAK,CAAC,CAAC;IAC5BrC,kBAAkB,CAAC,KAAK,CAAC;IACzBE,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,IAAIsB,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEc,QAAQ,KAAKd,KAAK,CAACc,QAAQ,CAACV,QAAQ,CAAC,eAAe,CAAC,IAAIJ,KAAK,CAACc,QAAQ,CAACV,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,IAAI;QACF,MAAMW,SAAS,GAAG,MAAMC,iBAAiB,CAAChB,KAAK,CAACc,QAAQ,CAAC;QACzDd,KAAK,CAACiB,cAAc,GAAGF,SAAS;MAClC,CAAC,CAAC,OAAO1D,KAAK,EAAE;QACdoC,OAAO,CAACyB,IAAI,CAAC,8CAA8C,CAAC;QAC5DlB,KAAK,CAACiB,cAAc,GAAGjB,KAAK,CAACc,QAAQ;MACvC;IACF;EACF,CAAC;EAED,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5B7C,mBAAmB,CAAC,EAAE,CAAC;IACvBF,oBAAoB,CAAC,IAAI,CAAC;IAC1BI,kBAAkB,CAAC,KAAK,CAAC;IACzBE,aAAa,CAAC,IAAI,CAAC;IACnB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAACyC,KAAK,CAAC,CAAC;IAClB;EACF,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC7C,kBAAkB,CAAC,CAACD,eAAe,CAAC;EACtC,CAAC;;EAED;EACA,MAAMyC,iBAAiB,GAAG,MAAOF,QAAQ,IAAK;IAC5C,IAAI,CAACA,QAAQ,EAAE,OAAOA,QAAQ;;IAE9B;IACA,IAAIA,QAAQ,CAACV,QAAQ,CAAC,eAAe,CAAC,IAAIU,QAAQ,CAACV,QAAQ,CAAC,KAAK,CAAC,EAAE;MAClE,IAAI;QACF,MAAMhB,QAAQ,GAAG,MAAMkC,KAAK,CAAE,6DAA4DC,kBAAkB,CAACT,QAAQ,CAAE,EAAC,EAAE;UACxHU,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,WAAW,EAAE;QACf,CAAC,CAAC;QAEF,IAAI,CAACtC,QAAQ,CAACuC,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAE,uBAAsBxC,QAAQ,CAACyC,MAAO,EAAC,CAAC;QAC3D;QAEA,MAAMxC,IAAI,GAAG,MAAMD,QAAQ,CAAC0C,IAAI,CAAC,CAAC;QAElC,IAAIzC,IAAI,CAACC,OAAO,IAAID,IAAI,CAAC0B,SAAS,EAAE;UAClCtB,OAAO,CAACsC,GAAG,CAAC,+BAA+B,CAAC;UAC5C,OAAO1C,IAAI,CAAC0B,SAAS;QACvB,CAAC,MAAM;UACLtB,OAAO,CAACyB,IAAI,CAAC,+CAA+C,EAAE7B,IAAI,CAAC;UACnE,OAAOyB,QAAQ;QACjB;MACF,CAAC,CAAC,OAAOzD,KAAK,EAAE;QACdoC,OAAO,CAACpC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,OAAOyD,QAAQ;MACjB;IACF;IAEA,OAAOA,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMkB,eAAe,GAAIhC,KAAK,IAAK;IACjC,IAAIA,KAAK,CAACiC,SAAS,EAAE;MACnB,OAAOjC,KAAK,CAACiC,SAAS;IACxB;IAEA,IAAIjC,KAAK,CAACkC,OAAO,IAAI,CAAClC,KAAK,CAACkC,OAAO,CAAC9B,QAAQ,CAAC,eAAe,CAAC,EAAE;MAC7D,IAAI+B,OAAO,GAAGnC,KAAK,CAACkC,OAAO;MAC3B,IAAIC,OAAO,CAAC/B,QAAQ,CAAC,aAAa,CAAC,IAAI+B,OAAO,CAAC/B,QAAQ,CAAC,UAAU,CAAC,EAAE;QACnE,MAAMgC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;QACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;MACtC;MACA,OAAQ,8BAA6BA,OAAQ,oBAAmB;IAClE;IAEA,OAAO,0BAA0B;EACnC,CAAC;;EAED;EACAlI,SAAS,CAAC,MAAM;IACd8E,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAEjB9E,SAAS,CAAC,MAAM;IACd,IAAI6C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEW,KAAK,IAAIX,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEc,SAAS,EAAE;MAClCJ,gBAAgB,CAACV,IAAI,CAACW,KAAK,CAAC;MAC5BE,gBAAgB,CAACb,IAAI,CAACc,SAAS,CAAC;IAClC;EACF,CAAC,EAAE,CAACd,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMuF,iBAAiB,GAAGA,CAAA,KAAM;IAC9BrE,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;EAED,MAAMsE,aAAa,GAAGA,CAAA,KAAM;IAC1BtE,aAAa,CAAC,EAAE,CAAC;IACjBF,kBAAkB,CAAC,KAAK,CAAC;IACzBiB,WAAW,CAAC,CAAC;EACf,CAAC;EAED,oBACEpC,OAAA;IAAKiB,SAAS,EAAC,0BAA0B;IAAA2E,QAAA,gBACvC5F,OAAA;MAAKiB,SAAS,EAAC,cAAc;MAAA2E,QAAA,eAC3B5F,OAAA;QAAKiB,SAAS,EAAC,sBAAsB;QAAA2E,QAAA,eACnC5F,OAAA;UAAKiB,SAAS,EAAC,qBAAqB;UAAA2E,QAAA,gBAClC5F,OAAA;YAAKiB,SAAS,EAAC,oBAAoB;YAAA2E,QAAA,eACjC5F,OAAA,CAACtB,OAAO;cAACuC,SAAS,EAAC;YAAiB;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACNhG,OAAA;YAAKiB,SAAS,EAAC,qBAAqB;YAAA2E,QAAA,gBAClC5F,OAAA;cAAIiB,SAAS,EAAC,aAAa;cAAA2E,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9ChG,OAAA;cAAGiB,SAAS,EAAC,gBAAgB;cAAA2E,QAAA,EAAC;YAE9B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhG,OAAA;MAAKiB,SAAS,EAAC,uBAAuB;MAAA2E,QAAA,gBAEpC5F,OAAA;QAAKiB,SAAS,EAAC,gBAAgB;QAAA2E,QAAA,gBAC7B5F,OAAA;UAAKiB,SAAS,EAAC,eAAe;UAAA2E,QAAA,gBAE5B5F,OAAA;YAAKiB,SAAS,EAAC,cAAc;YAAA2E,QAAA,gBAC3B5F,OAAA;cAAOiB,SAAS,EAAC,cAAc;cAAA2E,QAAA,gBAC7B5F,OAAA,CAACrB,QAAQ;gBAACsC,SAAS,EAAC;cAAa;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,SAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhG,OAAA;cACEiG,KAAK,EAAErF,aAAc;cACrBsF,QAAQ,EAAGC,CAAC,IAAKtF,gBAAgB,CAACsF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAClDhF,SAAS,EAAC,eAAe;cAAA2E,QAAA,gBAEzB5F,OAAA;gBAAQiG,KAAK,EAAC,SAAS;gBAAAL,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxChG,OAAA;gBAAQiG,KAAK,EAAC,WAAW;gBAAAL,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5ChG,OAAA;gBAAQiG,KAAK,EAAC,SAAS;gBAAAL,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNhG,OAAA;YAAKiB,SAAS,EAAC,cAAc;YAAA2E,QAAA,gBAC3B5F,OAAA;cAAOiB,SAAS,EAAC,cAAc;cAAA2E,QAAA,gBAC7B5F,OAAA,CAACrB,QAAQ;gBAACsC,SAAS,EAAC;cAAa;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,SAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhG,OAAA;cACEiG,KAAK,EAAElF,aAAc;cACrBmF,QAAQ,EAAGC,CAAC,IAAKnF,gBAAgB,CAACmF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAClDhF,SAAS,EAAC,eAAe;cAAA2E,QAAA,EAExB1D,gBAAgB,CAACmE,GAAG,CAAEC,GAAG,iBACxBtG,OAAA;gBAAkBiG,KAAK,EAAEK,GAAI;gBAAAV,QAAA,EAC1BU;cAAG,GADOA,GAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAER,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNhG,OAAA;YAAKiB,SAAS,EAAC,cAAc;YAAA2E,QAAA,gBAC3B5F,OAAA;cAAOiB,SAAS,EAAC,cAAc;cAAA2E,QAAA,gBAC7B5F,OAAA,CAACnB,QAAQ;gBAACoC,SAAS,EAAC;cAAa;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhG,OAAA;cACEiG,KAAK,EAAE/E,eAAgB;cACvBgF,QAAQ,EAAGC,CAAC,IAAKhF,kBAAkB,CAACgF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACpDhF,SAAS,EAAC,eAAe;cAAA2E,QAAA,gBAEzB5F,OAAA;gBAAQiG,KAAK,EAAC,KAAK;gBAAAL,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACxC7D,iBAAiB,CAACkE,GAAG,CAAE9D,OAAO,iBAC7BvC,OAAA;gBAAsBiG,KAAK,EAAE1D,OAAQ;gBAAAqD,QAAA,EAClCrD;cAAO,GADGA,OAAO;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEZ,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhG,OAAA;UAAKiB,SAAS,EAAC,mBAAmB;UAAA2E,QAAA,gBAChC5F,OAAA;YAAKiB,SAAS,EAAC,kBAAkB;YAAA2E,QAAA,gBAC/B5F,OAAA,CAACpB,QAAQ;cAACqC,SAAS,EAAC;YAAa;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpChG,OAAA;cACEuG,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,sCAAsC;cAClDP,KAAK,EAAE7E,UAAW;cAClB8E,QAAQ,EAAGC,CAAC,IAAK9E,aAAa,CAAC8E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/ChF,SAAS,EAAC;YAAc;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,EACD5E,UAAU,iBACTpB,OAAA;cAAQyG,OAAO,EAAEf,iBAAkB;cAACzE,SAAS,EAAC,kBAAkB;cAAA2E,QAAA,eAC9D5F,OAAA,CAACT,GAAG;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENhG,OAAA;YAAKiB,SAAS,EAAC,gBAAgB;YAAA2E,QAAA,gBAC7B5F,OAAA,CAAClB,eAAe;cAACmC,SAAS,EAAC;YAAW;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzChG,OAAA;cACEiG,KAAK,EAAE3E,MAAO;cACd4E,QAAQ,EAAGC,CAAC,IAAK5E,SAAS,CAAC4E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC3ChF,SAAS,EAAC,aAAa;cAAA2E,QAAA,gBAEvB5F,OAAA;gBAAQiG,KAAK,EAAC,QAAQ;gBAAAL,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5ChG,OAAA;gBAAQiG,KAAK,EAAC,QAAQ;gBAAAL,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5ChG,OAAA;gBAAQiG,KAAK,EAAC,OAAO;gBAAAL,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxChG,OAAA;gBAAQiG,KAAK,EAAC,SAAS;gBAAAL,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENhG,OAAA;YAAQyG,OAAO,EAAEd,aAAc;YAAC1E,SAAS,EAAC,aAAa;YAAA2E,QAAA,gBACrD5F,OAAA,CAAChB,UAAU;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WAEhB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLxF,OAAO,gBACNR,OAAA;QAAKiB,SAAS,EAAC,eAAe;QAAA2E,QAAA,gBAC5B5F,OAAA;UAAKiB,SAAS,EAAC;QAAiB;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvChG,OAAA;UAAA4F,QAAA,EAAG;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,GACJtF,KAAK,gBACPV,OAAA;QAAKiB,SAAS,EAAC,aAAa;QAAA2E,QAAA,gBAC1B5F,OAAA,CAACR,eAAe;UAACyB,SAAS,EAAC;QAAY;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1ChG,OAAA;UAAA4F,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BhG,OAAA;UAAA4F,QAAA,EAAIlF;QAAK;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdhG,OAAA;UAAQyG,OAAO,EAAErE,WAAY;UAACnB,SAAS,EAAC,WAAW;UAAA2E,QAAA,EAAC;QAEpD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJjD,uBAAuB,CAAC2D,MAAM,GAAG,CAAC,gBACpC1G,OAAA;QAAKiB,SAAS,EAAC,sDAAsD;QAAA2E,QAAA,EAClE7C,uBAAuB,CAACsD,GAAG,CAAC,CAAChD,KAAK,EAAEa,KAAK,kBACxClE,OAAA;UAAiBiB,SAAS,EAAC,YAAY;UAAA2E,QAAA,gBACrC5F,OAAA;YAAKiB,SAAS,EAAC,mBAAmB;YAAA2E,QAAA,gBAChC5F,OAAA;cAAKiB,SAAS,EAAC,iBAAiB;cAAA2E,QAAA,gBAC9B5F,OAAA,CAAC/B,OAAO;gBAAA4H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXhG,OAAA;gBAAA4F,QAAA,EAAM;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAENhG,OAAA;cAAKiB,SAAS,EAAC,kBAAkB;cAAA2E,QAAA,EAC9BvC,KAAK,CAACG;YAAK;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAENhG,OAAA;cAAKiB,SAAS,EAAC,iBAAiB;cAAA2E,QAAA,gBAC9B5F,OAAA;gBAAMiB,SAAS,EAAC,oBAAoB;gBAAA2E,QAAA,EAAEvC,KAAK,CAACd;cAAO;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3DhG,OAAA;gBAAMiB,SAAS,EAAC,kBAAkB;gBAAA2E,QAAA,GAAC,QAAM,EAACvC,KAAK,CAACpC,SAAS;cAAA;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL,CAAC3C,KAAK,CAACc,QAAQ,IAAId,KAAK,CAACkC,OAAO,kBAC/BvF,OAAA;YAAKiB,SAAS,EAAC,2BAA2B;YAACwF,OAAO,EAAEA,CAAA,KAAMxC,eAAe,CAACC,KAAK,CAAE;YAAA0B,QAAA,gBAC/E5F,OAAA;cACE2G,GAAG,EAAEtB,eAAe,CAAChC,KAAK,CAAE;cAC5BuD,GAAG,EAAEvD,KAAK,CAACG,KAAM;cACjBvC,SAAS,EAAC,iBAAiB;cAC3B4F,OAAO,EAAGV,CAAC,IAAK;gBACd;gBACA,IAAI9C,KAAK,CAACkC,OAAO,IAAI,CAAClC,KAAK,CAACkC,OAAO,CAAC9B,QAAQ,CAAC,eAAe,CAAC,EAAE;kBAC7D;kBACA,IAAI+B,OAAO,GAAGnC,KAAK,CAACkC,OAAO;kBAC3B,IAAIC,OAAO,CAAC/B,QAAQ,CAAC,aAAa,CAAC,IAAI+B,OAAO,CAAC/B,QAAQ,CAAC,UAAU,CAAC,EAAE;oBACnE,MAAMgC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;oBACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;kBACtC;kBAEA,MAAMsB,SAAS,GAAG,CACf,8BAA6BtB,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,cAAa,EACnD,0BAA0B,CAC3B;kBAED,MAAMuB,UAAU,GAAGZ,CAAC,CAACC,MAAM,CAACO,GAAG;kBAC/B,MAAMK,YAAY,GAAGF,SAAS,CAACG,SAAS,CAACC,GAAG,IAAIH,UAAU,CAACtD,QAAQ,CAACyD,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;kBAE1F,IAAIJ,YAAY,GAAGF,SAAS,CAACJ,MAAM,GAAG,CAAC,EAAE;oBACvCP,CAAC,CAACC,MAAM,CAACO,GAAG,GAAGG,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;kBAC5C;gBACF,CAAC,MAAM;kBACLb,CAAC,CAACC,MAAM,CAACO,GAAG,GAAG,0BAA0B;gBAC3C;cACF;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFhG,OAAA;cAAKiB,SAAS,EAAC,oBAAoB;cAAA2E,QAAA,eACjC5F,OAAA,CAAChC,YAAY;gBAACiD,SAAS,EAAC;cAAW;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACNhG,OAAA;cAAKiB,SAAS,EAAC,sBAAsB;cAAA2E,QAAA,EAClCvC,KAAK,CAACgE,QAAQ,IAAI;YAAO;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAEDhG,OAAA;YAAKiB,SAAS,EAAC,oBAAoB;YAAA2E,QAAA,eACjC5F,OAAA;cACEiB,SAAS,EAAC,oBAAoB;cAC9BwF,OAAO,EAAEA,CAAA,KAAMxC,eAAe,CAACC,KAAK,CAAE;cAAA0B,QAAA,gBAEtC5F,OAAA,CAACjB,MAAM;gBAAA8G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAEZ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,GArEE9B,KAAK;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsEV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENhG,OAAA;QAAKiB,SAAS,EAAC,aAAa;QAAA2E,QAAA,gBAC1B5F,OAAA,CAAC9B,eAAe;UAAC+C,SAAS,EAAC;QAAY;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1ChG,OAAA;UAAA4F,QAAA,EAAI;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBhG,OAAA;UAAA4F,QAAA,EAAG;QAA0D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACjEhG,OAAA;UAAGiB,SAAS,EAAC,YAAY;UAAA2E,QAAA,EAAC;QAA2C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLtE,gBAAgB,CAACgF,MAAM,GAAG,CAAC,IAAIlF,iBAAiB,KAAK,IAAI,iBACxDxB,OAAA;MAAKiB,SAAS,EAAG,iBAAgBW,eAAe,GAAG,UAAU,GAAG,EAAG,EAAE;MAAC6E,OAAO,EAAGN,CAAC,IAAK;QACpF,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACmB,aAAa,EAAE9C,eAAe,CAAC,CAAC;MACrD,CAAE;MAAAoB,QAAA,eACA5F,OAAA;QAAKiB,SAAS,EAAG,eAAcW,eAAe,GAAG,UAAU,GAAG,EAAG,EAAE;QAAAgE,QAAA,EAChE,CAAC,MAAM;UACN,MAAMvC,KAAK,GAAGN,uBAAuB,CAACvB,iBAAiB,CAAC;UACxD,IAAI,CAAC6B,KAAK,EAAE,oBAAOrD,OAAA;YAAA4F,QAAA,EAAK;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;UAE7C,oBACEhG,OAAA;YAAKiB,SAAS,EAAC,eAAe;YAAA2E,QAAA,gBAC5B5F,OAAA;cAAKiB,SAAS,EAAC,cAAc;cAAA2E,QAAA,gBAC3B5F,OAAA;gBAAKiB,SAAS,EAAC,YAAY;gBAAA2E,QAAA,gBACzB5F,OAAA;kBAAIiB,SAAS,EAAC,aAAa;kBAAA2E,QAAA,EAAEvC,KAAK,CAACG;gBAAK;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9ChG,OAAA;kBAAKiB,SAAS,EAAC,YAAY;kBAAA2E,QAAA,gBACzB5F,OAAA;oBAAMiB,SAAS,EAAC,eAAe;oBAAA2E,QAAA,EAAEvC,KAAK,CAACd;kBAAO;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtDhG,OAAA;oBAAMiB,SAAS,EAAC,aAAa;oBAAA2E,QAAA,GAAC,QAAM,EAACvC,KAAK,CAACpC,SAAS;kBAAA;oBAAA4E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC3D3C,KAAK,CAACvC,KAAK,iBAAId,OAAA;oBAAMiB,SAAS,EAAC,aAAa;oBAAA2E,QAAA,EAAEvC,KAAK,CAACvC;kBAAK;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNhG,OAAA;gBAAKiB,SAAS,EAAC,gBAAgB;gBAAA2E,QAAA,gBAC7B5F,OAAA;kBACEiB,SAAS,EAAC,wBAAwB;kBAClCwF,OAAO,EAAE/B,oBAAqB;kBAC9BlB,KAAK,EAAE5B,eAAe,GAAG,iBAAiB,GAAG,kBAAmB;kBAAAgE,QAAA,EAE/DhE,eAAe,gBAAG5B,OAAA,CAACvB,UAAU;oBAAAoH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGhG,OAAA,CAACxB,QAAQ;oBAAAqH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eACThG,OAAA;kBACEiB,SAAS,EAAC,uBAAuB;kBACjCwF,OAAO,EAAEjC,eAAgB;kBACzBhB,KAAK,EAAC,aAAa;kBAAAoC,QAAA,eAEnB5F,OAAA,CAAC3B,OAAO;oBAAAwH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNhG,OAAA;cAAKiB,SAAS,EAAC,iBAAiB;cAAA2E,QAAA,EAC7BvC,KAAK,CAACc,QAAQ,gBACbnE,OAAA;gBAAKuH,KAAK,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,MAAM;kBAAEC,YAAY,EAAE;gBAAM,CAAE;gBAAA9B,QAAA,gBACrE5F,OAAA;kBACE2H,GAAG,EAAGA,GAAG,IAAK1F,WAAW,CAAC0F,GAAG,CAAE;kBAC/BC,QAAQ;kBACRC,QAAQ;kBACRC,WAAW;kBACXC,OAAO,EAAC,UAAU;kBAClBC,KAAK,EAAC,MAAM;kBACZC,MAAM,EAAC,KAAK;kBACZC,MAAM,EAAE7C,eAAe,CAAChC,KAAK,CAAE;kBAC/BkE,KAAK,EAAE;oBACLS,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,OAAO;oBACfE,eAAe,EAAE;kBACnB,CAAE;kBACFtB,OAAO,EAAGV,CAAC,IAAK;oBACdpE,aAAa,CAAE,yBAAwBsB,KAAK,CAACG,KAAM,mCAAkC,CAAC;kBACxF,CAAE;kBACF4E,SAAS,EAAEA,CAAA,KAAM;oBACfrG,aAAa,CAAC,IAAI,CAAC;kBACrB,CAAE;kBACFsG,WAAW,EAAEA,CAAA,KAAM;oBACjBvF,OAAO,CAACsC,GAAG,CAAC,0BAA0B,CAAC;kBACzC,CAAE;kBACFkD,WAAW,EAAC,WAAW;kBAAA1C,QAAA,gBAGvB5F,OAAA;oBAAQ2G,GAAG,EAAEtD,KAAK,CAACiB,cAAc,IAAIjB,KAAK,CAACc,QAAS;oBAACoC,IAAI,EAAC;kBAAW;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAGvE3C,KAAK,CAACkF,SAAS,IAAIlF,KAAK,CAACkF,SAAS,CAAC7B,MAAM,GAAG,CAAC,IAAIrD,KAAK,CAACkF,SAAS,CAAClC,GAAG,CAAC,CAACmC,QAAQ,EAAEtE,KAAK,kBACpFlE,OAAA;oBAEEyI,IAAI,EAAC,WAAW;oBAChB9B,GAAG,EAAE6B,QAAQ,CAACtB,GAAI;oBAClBwB,OAAO,EAAEF,QAAQ,CAACG,QAAS;oBAC3BC,KAAK,EAAEJ,QAAQ,CAACK,YAAa;oBAC7BC,OAAO,EAAEN,QAAQ,CAACO,SAAS,IAAI7E,KAAK,KAAK;kBAAE,GALrC,GAAEsE,QAAQ,CAACG,QAAS,IAAGzE,KAAM,EAAC;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAMrC,CACF,CAAC,EAAC,8CAGL;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAGP3C,KAAK,CAACkF,SAAS,IAAIlF,KAAK,CAACkF,SAAS,CAAC7B,MAAM,GAAG,CAAC,iBAC5C1G,OAAA;kBAAKiB,SAAS,EAAC,oBAAoB;kBAAA2E,QAAA,gBACjC5F,OAAA,CAACL,WAAW;oBAACsB,SAAS,EAAC;kBAAe;oBAAA4E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACzChG,OAAA;oBAAA4F,QAAA,GAAM,yBAAuB,EAACvC,KAAK,CAACkF,SAAS,CAAC7B,MAAM,EAAC,cAAY;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CACN,EAGAlE,UAAU,iBACT9B,OAAA;kBAAKiB,SAAS,EAAC,qBAAqB;kBAAA2E,QAAA,eAClC5F,OAAA;oBAAKiB,SAAS,EAAC,eAAe;oBAAA2E,QAAA,gBAC5B5F,OAAA,CAACR,eAAe;sBAACyB,SAAS,EAAC;oBAAY;sBAAA4E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1ChG,OAAA;sBAAA4F,QAAA,EAAI9D;oBAAU;sBAAA+D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnBhG,OAAA;sBAAQyG,OAAO,EAAEA,CAAA,KAAM1E,aAAa,CAAC,IAAI,CAAE;sBAACd,SAAS,EAAC,mBAAmB;sBAAA2E,QAAA,EAAC;oBAE1E;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,GACN3C,KAAK,CAACkC,OAAO;cAAA;cACf;cACAvF,OAAA;gBACE2G,GAAG,EAAG,iCAAgCtD,KAAK,CAACkC,OAAQ,mBAAmB;gBACvE/B,KAAK,EAAEH,KAAK,CAACG,KAAM;gBACnBwF,WAAW,EAAC,GAAG;gBACfC,eAAe;gBACfhI,SAAS,EAAC,cAAc;gBACxBiI,MAAM,EAAEA,CAAA,KAAMpG,OAAO,CAACsC,GAAG,CAAC,yBAAyB;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,gBAEVhG,OAAA;gBAAKiB,SAAS,EAAC,aAAa;gBAAA2E,QAAA,gBAC1B5F,OAAA;kBAAKiB,SAAS,EAAC,YAAY;kBAAA2E,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpChG,OAAA;kBAAA4F,QAAA,EAAI;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1BhG,OAAA;kBAAA4F,QAAA,EAAI9D,UAAU,IAAI;gBAA4C;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnEhG,OAAA;kBAAKiB,SAAS,EAAC,eAAe;kBAAA2E,QAAA,eAC5B5F,OAAA;oBACEmJ,IAAI,EAAE9F,KAAK,CAACiB,cAAc,IAAIjB,KAAK,CAACc,QAAS;oBAC7CiC,MAAM,EAAC,QAAQ;oBACfgD,GAAG,EAAC,qBAAqB;oBACzBnI,SAAS,EAAC,mBAAmB;oBAAA2E,QAAA,EAC9B;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAEV,CAAC,EAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAC9F,EAAA,CA9jBQD,YAAY;EAAA,QACFpC,WAAW,EACXD,WAAW;AAAA;AAAAyL,EAAA,GAFrBpJ,YAAY;AAgkBrB,eAAeA,YAAY;AAAC,IAAAoJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}