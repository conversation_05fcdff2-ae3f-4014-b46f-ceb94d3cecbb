import React, { useState, useEffect, useCallback, useMemo } from "react";
import "./index.css";
import { motion, AnimatePresence } from "framer-motion";
import { getStudyMaterial } from "../../../apicalls/study";
import { useDispatch, useSelector } from "react-redux";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";

import {
  FaPlayCircle,
  FaVideo,
  FaGraduationCap,
  FaDownload,
  FaEye,
  FaTimes,
  FaChevronDown,
  FaSearch,
  FaExpand,
  FaCompress,
} from "react-icons/fa";
import {
  TbVideo,
  TbSchool,
  TbSearch,
  TbFilter,
  TbSortAscending,
  TbDownload,
  TbEye,
  TbCalendar,
  TbUser,
  TbChevronDown as TbChevronDownIcon,
  TbChevronUp,
  TbX,
  Tb<PERSON>lert<PERSON>riangle,
  TbIn<PERSON><PERSON><PERSON><PERSON>,
  Tb<PERSON><PERSON><PERSON>,
} from "react-icons/tb";
import { primarySubjects, secondarySubjects, advanceSubjects } from "../../../data/Subjects.jsx";

function VideoLessons() {
  const { user } = useSelector((state) => state.user);
  const dispatch = useDispatch();

  // State management
  const [videos, setVideos] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedLevel, setSelectedLevel] = useState(user?.level || "primary");
  const [selectedClass, setSelectedClass] = useState(user?.className || "1");
  const [selectedSubject, setSelectedSubject] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("newest");

  // Video player state
  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);
  const [showVideoIndices, setShowVideoIndices] = useState([]);
  const [isVideoExpanded, setIsVideoExpanded] = useState(false);
  const [videoError, setVideoError] = useState(null);
  const [videoRef, setVideoRef] = useState(null);

  // Available classes based on level
  const availableClasses = useMemo(() => {
    if (selectedLevel === "primary") return ["1", "2", "3", "4", "5", "6", "7"];
    if (selectedLevel === "secondary") return ["1", "2", "3", "4"];
    if (selectedLevel === "advance") return ["5", "6"];
    return [];
  }, [selectedLevel]);

  // Available subjects based on level
  const availableSubjects = useMemo(() => {
    if (selectedLevel === "primary") return primarySubjects;
    if (selectedLevel === "secondary") return secondarySubjects;
    if (selectedLevel === "advance") return advanceSubjects;
    return [];
  }, [selectedLevel]);

  // Fetch videos
  const fetchVideos = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      dispatch(ShowLoading());

      const filters = {
        level: selectedLevel,
        className: selectedClass,
        subject: selectedSubject === "all" ? "" : selectedSubject,
        content: "videos"
      };

      const response = await getStudyMaterial(filters);
      
      if (response?.data?.success) {
        setVideos(response.data.data || []);
      } else {
        setError(response?.data?.message || "Failed to fetch videos");
        setVideos([]);
      }
    } catch (error) {
      console.error("Error fetching videos:", error);
      setError("Failed to load videos. Please try again.");
      setVideos([]);
    } finally {
      setLoading(false);
      dispatch(HideLoading());
    }
  }, [selectedLevel, selectedClass, selectedSubject, dispatch]);

  // Filter and sort videos
  const filteredAndSortedVideos = useMemo(() => {
    let filtered = videos;

    // Apply search filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(video =>
        video.title?.toLowerCase().includes(searchLower) ||
        video.subject?.toLowerCase().includes(searchLower)
      );
    }

    // Apply sorting
    const sorted = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case "newest":
          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);
        case "oldest":
          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);
        case "title":
          return (a.title || "").localeCompare(b.title || "");
        case "subject":
          return (a.subject || "").localeCompare(b.subject || "");
        default:
          return 0;
      }
    });

    return sorted;
  }, [videos, searchTerm, sortBy]);

  // Video handlers
  const handleShowVideo = async (index) => {
    const video = filteredAndSortedVideos[index];

    setCurrentVideoIndex(index);
    setShowVideoIndices([index]);
    setIsVideoExpanded(false);
    setVideoError(null);

    // Get signed URL for S3 videos if needed
    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {
      try {
        const signedUrl = await getSignedVideoUrl(video.videoUrl);
        video.signedVideoUrl = signedUrl;
      } catch (error) {
        console.warn('Failed to get signed URL, using original URL');
        video.signedVideoUrl = video.videoUrl;
      }
    }
  };

  const handleHideVideo = () => {
    setShowVideoIndices([]);
    setCurrentVideoIndex(null);
    setIsVideoExpanded(false);
    setVideoError(null);
    if (videoRef) {
      videoRef.pause();
    }
  };

  const toggleVideoExpansion = () => {
    setIsVideoExpanded(!isVideoExpanded);
  };

  // Get signed URL for S3 videos to ensure access
  const getSignedVideoUrl = async (videoUrl) => {
    if (!videoUrl) return videoUrl;

    // For AWS S3 URLs, get signed URL from backend
    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {
      try {
        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success && data.signedUrl) {
          console.log('✅ Got signed URL for S3 video');
          return data.signedUrl;
        } else {
          console.warn('⚠️ Invalid response from signed URL endpoint:', data);
          return videoUrl;
        }
      } catch (error) {
        console.error('❌ Error getting signed URL:', error);
        return videoUrl;
      }
    }

    return videoUrl;
  };

  // Get thumbnail URL
  const getThumbnailUrl = (video) => {
    if (video.thumbnail) {
      return video.thumbnail;
    }
    
    if (video.videoID && !video.videoID.includes('amazonaws.com')) {
      let videoId = video.videoID;
      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {
        const match = videoId.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
        videoId = match ? match[1] : videoId;
      }
      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
    }
    
    return '/api/placeholder/400/225';
  };

  // Effects
  useEffect(() => {
    fetchVideos();
  }, [fetchVideos]);

  useEffect(() => {
    if (user?.level && user?.className) {
      setSelectedLevel(user.level);
      setSelectedClass(user.className);
    }
  }, [user]);

  // Clear search and refresh
  const handleClearSearch = () => {
    setSearchTerm("");
  };

  const handleRefresh = () => {
    setSearchTerm("");
    setSelectedSubject("all");
    fetchVideos();
  };

  return (
    <div className="study-material-container">
      <div className="study-header">
        <div className="study-header-content">
          <div className="study-title-section">
            <div className="study-icon-wrapper">
              <TbVideo className="study-main-icon" />
            </div>
            <div className="study-title-content">
              <h1 className="study-title">Video Lessons</h1>
              <p className="study-subtitle">
                Watch educational videos to enhance your learning
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="container-modern py-8">
        {/* Filters and Controls */}
        <div className="study-controls">
          <div className="study-filters">
            {/* Level Filter */}
            <div className="filter-group">
              <label className="filter-label">
                <TbSchool className="filter-icon" />
                Level
              </label>
              <select
                value={selectedLevel}
                onChange={(e) => setSelectedLevel(e.target.value)}
                className="filter-select"
              >
                <option value="primary">Primary</option>
                <option value="secondary">Secondary</option>
                <option value="advance">Advance</option>
              </select>
            </div>

            {/* Class Filter */}
            <div className="filter-group">
              <label className="filter-label">
                <TbSchool className="filter-icon" />
                Class
              </label>
              <select
                value={selectedClass}
                onChange={(e) => setSelectedClass(e.target.value)}
                className="filter-select"
              >
                {availableClasses.map((cls) => (
                  <option key={cls} value={cls}>
                    {cls}
                  </option>
                ))}
              </select>
            </div>

            {/* Subject Filter */}
            <div className="filter-group">
              <label className="filter-label">
                <TbFilter className="filter-icon" />
                Subject
              </label>
              <select
                value={selectedSubject}
                onChange={(e) => setSelectedSubject(e.target.value)}
                className="filter-select"
              >
                <option value="all">All Subjects</option>
                {availableSubjects.map((subject) => (
                  <option key={subject} value={subject}>
                    {subject}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Search and Sort */}
          <div className="study-search-sort">
            <div className="search-container">
              <TbSearch className="search-icon" />
              <input
                type="text"
                placeholder="Search videos by title or subject..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
              {searchTerm && (
                <button onClick={handleClearSearch} className="clear-search-btn">
                  <TbX />
                </button>
              )}
            </div>

            <div className="sort-container">
              <TbSortAscending className="sort-icon" />
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="sort-select"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="title">Title A-Z</option>
                <option value="subject">Subject A-Z</option>
              </select>
            </div>

            <button onClick={handleRefresh} className="refresh-btn">
              <TbDownload />
              Refresh
            </button>
          </div>
        </div>

        {/* Loading State */}
        {loading ? (
          <div className="loading-state">
            <div className="loading-spinner"></div>
            <p>Loading videos...</p>
          </div>
        ) : error ? (
          <div className="error-state">
            <TbAlertTriangle className="error-icon" />
            <h3>Error Loading Videos</h3>
            <p>{error}</p>
            <button onClick={fetchVideos} className="retry-btn">
              Try Again
            </button>
          </div>
        ) : filteredAndSortedVideos.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredAndSortedVideos.map((video, index) => (
              <div key={index} className="study-card">
                <div className="study-card-header">
                  <div className="study-card-meta">
                    <FaVideo />
                    <span>Video</span>
                  </div>

                  <div className="study-card-title">
                    {video.title}
                  </div>

                  <div className="study-card-info">
                    <span className="study-card-subject">{video.subject}</span>
                    <span className="study-card-class">Class {video.className}</span>
                  </div>
                </div>

                {/* Video Thumbnail */}
                {(video.videoUrl || video.videoID) && (
                  <div className="video-thumbnail-container" onClick={() => handleShowVideo(index)}>
                    <img
                      src={getThumbnailUrl(video)}
                      alt={video.title}
                      className="video-thumbnail"
                      onError={(e) => {
                        // Fallback logic for failed thumbnails
                        if (video.videoID && !video.videoID.includes('amazonaws.com')) {
                          // For YouTube videos, try different quality thumbnails
                          let videoId = video.videoID;
                          if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {
                            const match = videoId.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
                            videoId = match ? match[1] : videoId;
                          }

                          const fallbacks = [
                            `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,
                            `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,
                            `https://img.youtube.com/vi/${videoId}/default.jpg`,
                            '/api/placeholder/400/225'
                          ];

                          const currentSrc = e.target.src;
                          const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));

                          if (currentIndex < fallbacks.length - 1) {
                            e.target.src = fallbacks[currentIndex + 1];
                          }
                        } else {
                          e.target.src = '/api/placeholder/400/225';
                        }
                      }}
                    />
                    <div className="video-play-overlay">
                      <FaPlayCircle className="play-icon" />
                    </div>
                    <div className="video-duration-badge">
                      {video.duration || "Video"}
                    </div>
                  </div>
                )}

                <div className="study-card-actions">
                  <button
                    className="action-btn primary"
                    onClick={() => handleShowVideo(index)}
                  >
                    <FaPlayCircle />
                    Watch Video
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="empty-state">
            <FaGraduationCap className="empty-icon" />
            <h3>No Videos Found</h3>
            <p>No video lessons are available for your current selection.</p>
            <p className="suggestion">Try selecting a different class or subject.</p>
          </div>
        )}
      </div>

      {/* Enhanced Video Display */}
      {showVideoIndices.length > 0 && currentVideoIndex !== null && (
        <div className={`video-overlay ${isVideoExpanded ? 'expanded' : ''}`} onClick={(e) => {
          if (e.target === e.currentTarget) handleHideVideo();
        }}>
          <div className={`video-modal ${isVideoExpanded ? 'expanded' : ''}`}>
            {(() => {
              const video = filteredAndSortedVideos[currentVideoIndex];
              if (!video) return <div>Video not found</div>;

              return (
                <div className="video-content">
                  <div className="video-header">
                    <div className="video-info">
                      <h3 className="video-title">{video.title}</h3>
                      <div className="video-meta">
                        <span className="video-subject">{video.subject}</span>
                        <span className="video-class">Class {video.className}</span>
                        {video.level && <span className="video-level">{video.level}</span>}
                      </div>
                    </div>
                    <div className="video-controls">
                      <button
                        className="control-btn expand-btn"
                        onClick={toggleVideoExpansion}
                        title={isVideoExpanded ? "Exit fullscreen" : "Enter fullscreen"}
                      >
                        {isVideoExpanded ? <FaCompress /> : <FaExpand />}
                      </button>
                      <button
                        className="control-btn close-btn"
                        onClick={handleHideVideo}
                        title="Close video"
                      >
                        <FaTimes />
                      </button>
                    </div>
                  </div>
                  <div className="video-container">
                    {video.videoUrl ? (
                      <div style={{ padding: '15px', background: '#000', borderRadius: '8px' }}>
                          <video
                            ref={(ref) => setVideoRef(ref)}
                            controls
                            autoPlay
                            playsInline
                            preload="metadata"
                            width="100%"
                            height="400"
                            poster={getThumbnailUrl(video)}
                            style={{
                              width: '100%',
                              height: '400px',
                              backgroundColor: '#000'
                            }}
                            onError={(e) => {
                              setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);
                            }}
                            onCanPlay={() => {
                              setVideoError(null);
                            }}
                            onLoadStart={() => {
                              console.log('🎬 Video loading started');
                            }}
                            crossOrigin="anonymous"
                          >
                            {/* Use signed URL if available, otherwise use original URL */}
                            <source src={video.signedVideoUrl || video.videoUrl} type="video/mp4" />

                            {/* Add subtitle tracks if available */}
                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (
                              <track
                                key={`${subtitle.language}-${index}`}
                                kind="subtitles"
                                src={subtitle.url}
                                srcLang={subtitle.language}
                                label={subtitle.languageName}
                                default={subtitle.isDefault || index === 0}
                              />
                            ))}

                            Your browser does not support the video tag.
                          </video>

                          {/* Subtitle indicator */}
                          {video.subtitles && video.subtitles.length > 0 && (
                            <div className="subtitle-indicator">
                              <TbInfoCircle className="subtitle-icon" />
                              <span>Subtitles available in {video.subtitles.length} language(s)</span>
                            </div>
                          )}

                          {/* Video error display */}
                          {videoError && (
                            <div className="video-error-overlay">
                              <div className="error-content">
                                <TbAlertTriangle className="error-icon" />
                                <p>{videoError}</p>
                                <button onClick={() => setVideoError(null)} className="dismiss-error-btn">
                                  Dismiss
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                    ) : video.videoID ? (
                      // Fallback to YouTube embed if no videoUrl
                      <iframe
                        src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}
                        title={video.title}
                        frameBorder="0"
                        allowFullScreen
                        className="video-iframe"
                        onLoad={() => console.log('✅ YouTube iframe loaded')}
                      ></iframe>
                    ) : (
                      <div className="video-error">
                        <div className="error-icon">⚠️</div>
                        <h3>Video Unavailable</h3>
                        <p>{videoError || "This video cannot be played at the moment."}</p>
                        <div className="error-actions">
                          <a
                            href={video.signedVideoUrl || video.videoUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="external-link-btn"
                          >
                            📱 Open in New Tab
                          </a>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              );
            })()}
          </div>
        </div>
      )}
    </div>
  );
}

export default VideoLessons;
