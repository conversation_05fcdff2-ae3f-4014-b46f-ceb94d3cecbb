/* Import the existing StudyMaterial styles */
@import '../StudyMaterial/index.css';

/* VideoLessons specific overrides and additions */
.video-lessons-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem 0;
}

.video-lessons-header {
  text-align: center;
  margin-bottom: 2rem;
  color: white;
}

.video-lessons-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.video-lessons-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 2rem;
}

/* Video grid specific styles */
.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  padding: 1rem 0;
}

.video-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.video-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.video-thumbnail-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  cursor: pointer;
}

.video-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.video-thumbnail-container:hover .video-thumbnail {
  transform: scale(1.05);
}

.video-play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.video-play-overlay:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: translate(-50%, -50%) scale(1.1);
}

.play-icon {
  color: white;
  font-size: 24px;
}

.video-duration-badge {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.video-card-content {
  padding: 1rem;
}

.video-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #2d3748;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-card-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #667eea;
  font-size: 0.9rem;
  font-weight: 500;
}

.video-card-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.video-card-subject {
  background: #e2e8f0;
  color: #4a5568;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.video-card-class {
  color: #718096;
  font-size: 0.8rem;
}

/* Video modal styles */
.video-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.video-overlay.expanded {
  padding: 0;
}

.video-modal {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  max-width: 90vw;
  max-height: 90vh;
  width: 100%;
  position: relative;
}

.video-modal.expanded {
  max-width: 100vw;
  max-height: 100vh;
  border-radius: 0;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f7fafc;
  border-bottom: 1px solid #e2e8f0;
}

.video-info h3 {
  margin: 0;
  color: #2d3748;
  font-size: 1.2rem;
  font-weight: 600;
}

.video-meta {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
  font-size: 0.9rem;
  color: #718096;
}

.video-controls {
  display: flex;
  gap: 0.5rem;
}

.control-btn {
  background: none;
  border: none;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  color: #4a5568;
  transition: all 0.2s ease;
}

.control-btn:hover {
  background: #e2e8f0;
  color: #2d3748;
}

.video-container {
  position: relative;
  background: #000;
}

.video-iframe {
  width: 100%;
  height: 400px;
  border: none;
}

.video-modal.expanded .video-iframe {
  height: calc(100vh - 80px);
}

.subtitle-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 0.8rem;
  border-radius: 4px;
  margin-top: 0.5rem;
}

.subtitle-icon {
  font-size: 1rem;
}

.video-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
}

.error-content {
  padding: 2rem;
}

.error-content .error-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #f56565;
}

.dismiss-error-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  margin-top: 1rem;
  transition: background 0.2s ease;
}

.dismiss-error-btn:hover {
  background: #5a67d8;
}

.video-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  color: white;
  min-height: 400px;
}

.video-error .error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.video-error h3 {
  margin-bottom: 1rem;
  color: white;
}

.external-link-btn {
  background: #667eea;
  color: white;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  display: inline-block;
  margin-top: 1rem;
  transition: background 0.2s ease;
}

.external-link-btn:hover {
  background: #5a67d8;
  color: white;
  text-decoration: none;
}

/* Responsive design */
@media (max-width: 768px) {
  .video-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .video-overlay {
    padding: 1rem;
  }
  
  .video-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .video-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .video-iframe {
    height: 250px;
  }
  
  .video-modal.expanded .video-iframe {
    height: calc(100vh - 120px);
  }
}
