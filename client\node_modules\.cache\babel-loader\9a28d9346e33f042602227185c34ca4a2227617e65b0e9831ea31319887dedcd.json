{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { FaPlayCircle, FaVideo, FaGraduationCap, FaDownload, FaEye, FaTimes, FaChevronDown, FaSearch, FaExpand, FaCompress } from \"react-icons/fa\";\nimport { TbVideo, TbSchool, <PERSON>b<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Tb<PERSON>ortAscending, Tb<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON><PERSON>, TbChevronDown as TbChevronDownIcon, TbChevronUp, TbX, TbAlertTriangle, TbInfoCircle, TbCheck } from \"react-icons/tb\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction VideoLessons() {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const dispatch = useDispatch();\n\n  // State management\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedLevel, setSelectedLevel] = useState((user === null || user === void 0 ? void 0 : user.level) || \"primary\");\n  const [selectedClass, setSelectedClass] = useState(\"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Video player state\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [videoError, setVideoError] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n\n  // Comments state\n  const [comments, setComments] = useState([]);\n  const [newComment, setNewComment] = useState(\"\");\n  const [replyingTo, setReplyingTo] = useState(null);\n  const [replyText, setReplyText] = useState(\"\");\n\n  // Available classes based on level\n  const availableClasses = useMemo(() => {\n    if (selectedLevel === \"primary\") return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    if (selectedLevel === \"secondary\") return [\"1\", \"2\", \"3\", \"4\"];\n    if (selectedLevel === \"advance\") return [\"5\", \"6\"];\n    return [];\n  }, [selectedLevel]);\n\n  // Available subjects based on level\n  const availableSubjects = useMemo(() => {\n    if (selectedLevel === \"primary\") return primarySubjects;\n    if (selectedLevel === \"secondary\") return secondarySubjects;\n    if (selectedLevel === \"advance\") return advanceSubjects;\n    return [];\n  }, [selectedLevel]);\n\n  // Fetch videos\n  const fetchVideos = useCallback(async () => {\n    try {\n      var _response$data;\n      setLoading(true);\n      setError(null);\n      dispatch(ShowLoading());\n      const filters = {\n        level: selectedLevel,\n        className: \"\",\n        // Get all classes for the level\n        subject: \"\",\n        // Get all subjects for the level\n        content: \"videos\"\n      };\n      const response = await getStudyMaterial(filters);\n      if (response !== null && response !== void 0 && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.success) {\n        setVideos(response.data.data || []);\n      } else {\n        var _response$data2;\n        setError((response === null || response === void 0 ? void 0 : (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message) || \"Failed to fetch videos\");\n        setVideos([]);\n      }\n    } catch (error) {\n      console.error(\"Error fetching videos:\", error);\n      setError(\"Failed to load videos. Please try again.\");\n      setVideos([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [selectedLevel, dispatch]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos;\n\n    // Apply level filter\n    filtered = filtered.filter(video => video.level === selectedLevel);\n\n    // Apply class filter\n    if (selectedClass !== \"all\") {\n      filtered = filtered.filter(video => video.className === selectedClass);\n    }\n\n    // Apply subject filter\n    if (selectedSubject !== \"all\") {\n      filtered = filtered.filter(video => video.subject === selectedSubject);\n    }\n\n    // Apply search filter\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(video => {\n        var _video$title, _video$subject, _video$topic;\n        return ((_video$title = video.title) === null || _video$title === void 0 ? void 0 : _video$title.toLowerCase().includes(searchLower)) || ((_video$subject = video.subject) === null || _video$subject === void 0 ? void 0 : _video$subject.toLowerCase().includes(searchLower)) || ((_video$topic = video.topic) === null || _video$topic === void 0 ? void 0 : _video$topic.toLowerCase().includes(searchLower));\n      });\n    }\n\n    // Apply sorting\n    const sorted = [...filtered].sort((a, b) => {\n      switch (sortBy) {\n        case \"newest\":\n          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);\n        case \"oldest\":\n          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);\n        case \"title\":\n          return (a.title || \"\").localeCompare(b.title || \"\");\n        case \"subject\":\n          return (a.subject || \"\").localeCompare(b.subject || \"\");\n        default:\n          return 0;\n      }\n    });\n    return sorted;\n  }, [videos, searchTerm, sortBy, selectedLevel, selectedClass, selectedSubject]);\n\n  // Video handlers\n  const handleShowVideo = async index => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video !== null && video !== void 0 && video.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    if (videoRef) {\n      videoRef.pause();\n    }\n  };\n  const toggleVideoExpansion = () => {\n    setIsVideoExpanded(!isVideoExpanded);\n  };\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async videoUrl => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.error('❌ Error getting signed URL:', error);\n        return videoUrl;\n      }\n    }\n    return videoUrl;\n  };\n\n  // Get thumbnail URL\n  const getThumbnailUrl = video => {\n    if (video.thumbnail) {\n      return video.thumbnail;\n    }\n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      let videoId = video.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    return '/api/placeholder/400/225';\n  };\n\n  // Effects\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.level) {\n      setSelectedLevel(user.level);\n    }\n    if (user !== null && user !== void 0 && user.class) {\n      setSelectedClass(user.class);\n    }\n  }, [user]);\n\n  // Clear search and refresh\n  const handleClearSearch = () => {\n    setSearchTerm(\"\");\n  };\n  const handleRefresh = () => {\n    // Only refresh data, don't clear filters or search\n    fetchVideos();\n  };\n  const handleClearAll = () => {\n    setSearchTerm(\"\");\n    setSelectedSubject(\"all\");\n    setSelectedClass(\"all\");\n    fetchVideos();\n  };\n\n  // Comment functions\n  const handleAddComment = () => {\n    if (newComment.trim()) {\n      const comment = {\n        id: Date.now(),\n        text: newComment,\n        author: (user === null || user === void 0 ? void 0 : user.name) || \"Anonymous\",\n        timestamp: new Date().toISOString(),\n        replies: []\n      };\n      setComments([...comments, comment]);\n      setNewComment(\"\");\n    }\n  };\n  const handleAddReply = commentId => {\n    if (replyText.trim()) {\n      const reply = {\n        id: Date.now(),\n        text: replyText,\n        author: (user === null || user === void 0 ? void 0 : user.name) || \"Anonymous\",\n        timestamp: new Date().toISOString()\n      };\n      setComments(comments.map(comment => comment.id === commentId ? {\n        ...comment,\n        replies: [...comment.replies, reply]\n      } : comment));\n      setReplyText(\"\");\n      setReplyingTo(null);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-lessons-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-main\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-icon\",\n            children: /*#__PURE__*/_jsxDEV(TbVideo, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              children: \"Video Lessons\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Watch educational videos to enhance your learning\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"level-display\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"current-level\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"level-label\",\n              children: \"Level:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"level-value\",\n              children: selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"current-class\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"class-label\",\n              children: \"Your Class:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"class-value\",\n              children: (user === null || user === void 0 ? void 0 : user.level) === 'primary' ? `Class ${(user === null || user === void 0 ? void 0 : user.class) || 'N/A'}` : (user === null || user === void 0 ? void 0 : user.level) === 'secondary' ? `Form ${(user === null || user === void 0 ? void 0 : user.class) || 'N/A'}` : (user === null || user === void 0 ? void 0 : user.level) === 'advance' ? `Form ${(user === null || user === void 0 ? void 0 : user.class) || 'N/A'}` : 'Not Set'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"controls-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbSchool, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this), \"Level\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedLevel,\n              onChange: e => setSelectedLevel(e.target.value),\n              className: \"control-select level-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"primary\",\n                children: \"Primary (1-7)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"secondary\",\n                children: \"Secondary (1-4)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"advance\",\n                children: \"Advance (5-6)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbFilter, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this), \"Filter by Class\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedClass,\n              onChange: e => setSelectedClass(e.target.value),\n              className: \"control-select class-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Classes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 17\n              }, this), availableClasses.map(cls => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: cls,\n                children: selectedLevel === 'primary' ? `Class ${cls}` : `Form ${cls}`\n              }, cls, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbFilter, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 17\n              }, this), \"Subject\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedSubject,\n              onChange: e => setSelectedSubject(e.target.value),\n              className: \"control-select subject-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Subjects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 17\n              }, this), availableSubjects.map(subject => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: subject,\n                children: subject\n              }, subject, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbSortAscending, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 17\n              }, this), \"Sort\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: sortBy,\n              onChange: e => setSortBy(e.target.value),\n              className: \"control-select sort-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"newest\",\n                children: \"Newest First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"oldest\",\n                children: \"Oldest First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"title\",\n                children: \"Title A-Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"subject\",\n                children: \"Subject A-Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-container\",\n            children: [/*#__PURE__*/_jsxDEV(TbSearch, {\n              className: \"search-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search videos by title, subject, or topic...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              className: \"search-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 15\n            }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleClearSearch,\n              className: \"clear-search-btn\",\n              children: [/*#__PURE__*/_jsxDEV(TbX, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 19\n              }, this), \"Clear Search\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRefresh,\n            className: \"refresh-btn\",\n            children: [/*#__PURE__*/_jsxDEV(TbDownload, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this), \"Refresh All\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading videos...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-state\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n          className: \"error-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Error Loading Videos\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchVideos,\n          className: \"retry-btn\",\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 11\n      }, this) : filteredAndSortedVideos.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"videos-grid\",\n        children: filteredAndSortedVideos.map((video, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"video-card\",\n          onClick: () => handleShowVideo(index),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-card-thumbnail\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: getThumbnailUrl(video),\n              alt: video.title,\n              className: \"thumbnail-image\",\n              onError: e => {\n                // Fallback logic for failed thumbnails\n                if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                  // For YouTube videos, try different quality thumbnails\n                  let videoId = video.videoID;\n                  if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                    const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                    videoId = match ? match[1] : videoId;\n                  }\n                  const fallbacks = [`https://img.youtube.com/vi/${videoId}/hqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/default.jpg`, '/api/placeholder/320/180'];\n                  const currentSrc = e.target.src;\n                  const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                  if (currentIndex < fallbacks.length - 1) {\n                    e.target.src = fallbacks[currentIndex + 1];\n                  }\n                } else {\n                  e.target.src = '/api/placeholder/320/180';\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"play-overlay\",\n              children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                className: \"play-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-duration\",\n              children: video.duration || \"Video\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 19\n            }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"subtitle-badge\",\n              children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 23\n              }, this), \"CC\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-card-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"video-title\",\n              children: video.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"video-subject\",\n                children: video.subject\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"video-class\",\n                children: selectedLevel === 'primary' ? `Class ${video.className}` : `Form ${video.className}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-tags\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"level-tag\",\n                children: video.level\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 21\n              }, this), video.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"topic-tag\",\n                children: video.topic\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 37\n              }, this), video.sharedFromClass && video.sharedFromClass !== video.className && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"shared-tag\",\n                children: [\"Shared from \", selectedLevel === 'primary' ? `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 470,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n          className: \"empty-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No Videos Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No video lessons are available for your current selection.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"suggestion\",\n          children: \"Try selecting a different class or subject.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 542,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 7\n    }, this), showVideoIndices.length > 0 && currentVideoIndex !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `video-overlay ${isVideoExpanded ? 'expanded' : ''}`,\n      onClick: e => {\n        if (e.target === e.currentTarget) handleHideVideo();\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `video-modal ${isVideoExpanded ? 'expanded' : ''}`,\n        children: (() => {\n          const video = filteredAndSortedVideos[currentVideoIndex];\n          if (!video) return /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Video not found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 559,\n            columnNumber: 34\n          }, this);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"video-title\",\n                  children: video.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-subject\",\n                    children: video.subject\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 567,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-class\",\n                    children: [\"Class \", video.className]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 568,\n                    columnNumber: 25\n                  }, this), video.level && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-level\",\n                    children: video.level\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 569,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 566,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-controls\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"control-btn expand-btn\",\n                  onClick: toggleVideoExpansion,\n                  title: isVideoExpanded ? \"Exit fullscreen\" : \"Enter fullscreen\",\n                  children: isVideoExpanded ? /*#__PURE__*/_jsxDEV(FaCompress, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 578,\n                    columnNumber: 44\n                  }, this) : /*#__PURE__*/_jsxDEV(FaExpand, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 578,\n                    columnNumber: 61\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 573,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"control-btn close-btn\",\n                  onClick: handleHideVideo,\n                  title: \"Close video\",\n                  children: /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 585,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-container\",\n              children: video.videoUrl ? /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '15px',\n                  background: '#000',\n                  borderRadius: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"video\", {\n                  ref: ref => setVideoRef(ref),\n                  controls: true,\n                  autoPlay: true,\n                  playsInline: true,\n                  preload: \"metadata\",\n                  width: \"100%\",\n                  height: \"400\",\n                  poster: getThumbnailUrl(video),\n                  style: {\n                    width: '100%',\n                    height: '400px',\n                    backgroundColor: '#000'\n                  },\n                  onError: e => {\n                    setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                  },\n                  onCanPlay: () => {\n                    setVideoError(null);\n                  },\n                  onLoadStart: () => {\n                    console.log('🎬 Video loading started');\n                  },\n                  crossOrigin: \"anonymous\",\n                  children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                    src: video.signedVideoUrl || video.videoUrl,\n                    type: \"video/mp4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 618,\n                    columnNumber: 29\n                  }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => /*#__PURE__*/_jsxDEV(\"track\", {\n                    kind: \"subtitles\",\n                    src: subtitle.url,\n                    srcLang: subtitle.language,\n                    label: subtitle.languageName,\n                    default: subtitle.isDefault || index === 0\n                  }, `${subtitle.language}-${index}`, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 622,\n                    columnNumber: 31\n                  }, this)), \"Your browser does not support the video tag.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 27\n                }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"subtitle-indicator\",\n                  children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {\n                    className: \"subtitle-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 638,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Subtitles available in \", video.subtitles.length, \" language(s)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 639,\n                    columnNumber: 31\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 637,\n                  columnNumber: 29\n                }, this), videoError && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-error-overlay\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"error-content\",\n                    children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n                      className: \"error-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 647,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: videoError\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 648,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setVideoError(null),\n                      className: \"dismiss-error-btn\",\n                      children: \"Dismiss\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 649,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 646,\n                    columnNumber: 31\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 645,\n                  columnNumber: 29\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 23\n              }, this) : video.videoID ?\n              /*#__PURE__*/\n              // Fallback to YouTube embed if no videoUrl\n              _jsxDEV(\"iframe\", {\n                src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                title: video.title,\n                frameBorder: \"0\",\n                allowFullScreen: true,\n                className: \"video-iframe\",\n                onLoad: () => console.log('✅ YouTube iframe loaded')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 658,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-error\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"error-icon\",\n                  children: \"\\u26A0\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"Video Unavailable\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 669,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: videoError || \"This video cannot be played at the moment.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 670,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"error-actions\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: video.signedVideoUrl || video.videoUrl,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: \"external-link-btn\",\n                    children: \"\\uD83D\\uDCF1 Open in New Tab\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 672,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 671,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 667,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 19\n            }, this), !isVideoExpanded && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"comments-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"comments-title\",\n                children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 689,\n                  columnNumber: 25\n                }, this), \"Discussion (\", comments.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"add-comment\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"comment-input-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                    value: newComment,\n                    onChange: e => setNewComment(e.target.value),\n                    placeholder: \"Share your thoughts about this video...\",\n                    className: \"comment-input\",\n                    rows: \"3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 696,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: handleAddComment,\n                    className: \"comment-submit-btn\",\n                    disabled: !newComment.trim(),\n                    children: \"Post Comment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 703,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 695,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"comments-list\",\n                children: comments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"no-comments\",\n                  children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 717,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"No comments yet. Be the first to share your thoughts!\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 718,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 716,\n                  columnNumber: 27\n                }, this) : comments.map(comment => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"comment\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"comment-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"comment-author\",\n                      children: comment.author\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 724,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"comment-time\",\n                      children: new Date(comment.timestamp).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 725,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 723,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"comment-text\",\n                    children: comment.text\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 729,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"comment-actions\",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setReplyingTo(comment.id),\n                      className: \"reply-btn\",\n                      children: \"Reply\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 731,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 730,\n                    columnNumber: 31\n                  }, this), replyingTo === comment.id && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"reply-input-container\",\n                    children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                      value: replyText,\n                      onChange: e => setReplyText(e.target.value),\n                      placeholder: \"Write a reply...\",\n                      className: \"reply-input\",\n                      rows: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 742,\n                      columnNumber: 35\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"reply-actions\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleAddReply(comment.id),\n                        className: \"reply-submit-btn\",\n                        disabled: !replyText.trim(),\n                        children: \"Reply\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 750,\n                        columnNumber: 37\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setReplyingTo(null);\n                          setReplyText(\"\");\n                        },\n                        className: \"reply-cancel-btn\",\n                        children: \"Cancel\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 757,\n                        columnNumber: 37\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 749,\n                      columnNumber: 35\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 741,\n                    columnNumber: 33\n                  }, this), comment.replies.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"replies\",\n                    children: comment.replies.map(reply => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"reply\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"reply-header\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"reply-author\",\n                          children: reply.author\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 776,\n                          columnNumber: 41\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"reply-time\",\n                          children: new Date(reply.timestamp).toLocaleDateString()\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 777,\n                          columnNumber: 41\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 775,\n                        columnNumber: 39\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"reply-text\",\n                        children: reply.text\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 781,\n                        columnNumber: 39\n                      }, this)]\n                    }, reply.id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 774,\n                      columnNumber: 37\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 772,\n                    columnNumber: 33\n                  }, this)]\n                }, comment.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 722,\n                  columnNumber: 29\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 714,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 17\n          }, this);\n        })()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 556,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 315,\n    columnNumber: 5\n  }, this);\n}\n_s(VideoLessons, \"2RM1C8y+aNUMRZ203dp0PcHuV+c=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = VideoLessons;\nexport default VideoLessons;\nvar _c;\n$RefreshReg$(_c, \"VideoLessons\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "motion", "AnimatePresence", "getStudyMaterial", "useDispatch", "useSelector", "HideLoading", "ShowLoading", "FaPlayCircle", "FaVideo", "FaGraduationCap", "FaDownload", "FaEye", "FaTimes", "FaChevronDown", "FaSearch", "FaExpand", "FaCompress", "TbVideo", "TbSchool", "TbSearch", "Tb<PERSON><PERSON>er", "TbSortAscending", "TbDownload", "TbEye", "TbCalendar", "TbUser", "TbChevronDown", "TbChevronDownIcon", "TbChevronUp", "TbX", "TbAlertTriangle", "TbInfoCircle", "TbCheck", "primarySubjects", "secondarySubjects", "advanceSubjects", "jsxDEV", "_jsxDEV", "VideoLessons", "_s", "user", "state", "dispatch", "videos", "setVideos", "loading", "setLoading", "error", "setError", "selectedLevel", "setSelectedLevel", "level", "selectedClass", "setSelectedClass", "selectedSubject", "setSelectedSubject", "searchTerm", "setSearchTerm", "sortBy", "setSortBy", "currentVideoIndex", "setCurrentVideoIndex", "showVideoIndices", "setShowVideoIndices", "isVideoExpanded", "setIsVideoExpanded", "videoError", "setVideoError", "videoRef", "setVideoRef", "comments", "setComments", "newComment", "setNewComment", "replyingTo", "setReplyingTo", "replyText", "setReplyText", "availableClasses", "availableSubjects", "fetchVideos", "_response$data", "filters", "className", "subject", "content", "response", "data", "success", "_response$data2", "message", "console", "filteredAndSortedVideos", "filtered", "filter", "video", "trim", "searchLower", "toLowerCase", "_video$title", "_video$subject", "_video$topic", "title", "includes", "topic", "sorted", "sort", "a", "b", "Date", "createdAt", "localeCompare", "handleShowVideo", "index", "videoUrl", "signedUrl", "getSignedVideoUrl", "signedVideoUrl", "warn", "handleHideVideo", "pause", "toggleVideoExpansion", "fetch", "encodeURIComponent", "method", "headers", "ok", "Error", "status", "json", "log", "getThumbnailUrl", "thumbnail", "videoID", "videoId", "match", "class", "handleClearSearch", "handleRefresh", "handleClearAll", "handleAddComment", "comment", "id", "now", "text", "author", "name", "timestamp", "toISOString", "replies", "handleAddReply", "commentId", "reply", "map", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "char<PERSON>t", "toUpperCase", "slice", "value", "onChange", "e", "target", "cls", "type", "placeholder", "onClick", "length", "src", "alt", "onError", "fallbacks", "currentSrc", "currentIndex", "findIndex", "url", "split", "pop", "duration", "subtitles", "sharedFromClass", "currentTarget", "style", "padding", "background", "borderRadius", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "poster", "backgroundColor", "onCanPlay", "onLoadStart", "crossOrigin", "subtitle", "kind", "srcLang", "language", "label", "languageName", "default", "isDefault", "frameBorder", "allowFullScreen", "onLoad", "href", "rel", "rows", "disabled", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/index.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\n\nimport {\n  FaPlayCircle,\n  FaVideo,\n  FaGraduationCap,\n  FaDownload,\n  FaEye,\n  FaTimes,\n  FaChevronDown,\n  FaSearch,\n  FaExpand,\n  FaCompress,\n} from \"react-icons/fa\";\nimport {\n  TbVideo,\n  TbSchool,\n  TbSearch,\n  TbFilter,\n  TbSortAscending,\n  TbDownload,\n  TbEye,\n  TbCalendar,\n  TbUser,\n  TbChevronDown as TbChevronDownIcon,\n  TbChevronUp,\n  TbX,\n  Tb<PERSON>lert<PERSON>riangle,\n  TbIn<PERSON><PERSON><PERSON><PERSON>,\n  Tb<PERSON><PERSON><PERSON>,\n} from \"react-icons/tb\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\n\nfunction VideoLessons() {\n  const { user } = useSelector((state) => state.user);\n  const dispatch = useDispatch();\n\n  // State management\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedLevel, setSelectedLevel] = useState(user?.level || \"primary\");\n  const [selectedClass, setSelectedClass] = useState(\"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Video player state\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [videoError, setVideoError] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n\n  // Comments state\n  const [comments, setComments] = useState([]);\n  const [newComment, setNewComment] = useState(\"\");\n  const [replyingTo, setReplyingTo] = useState(null);\n  const [replyText, setReplyText] = useState(\"\");\n\n  // Available classes based on level\n  const availableClasses = useMemo(() => {\n    if (selectedLevel === \"primary\") return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    if (selectedLevel === \"secondary\") return [\"1\", \"2\", \"3\", \"4\"];\n    if (selectedLevel === \"advance\") return [\"5\", \"6\"];\n    return [];\n  }, [selectedLevel]);\n\n  // Available subjects based on level\n  const availableSubjects = useMemo(() => {\n    if (selectedLevel === \"primary\") return primarySubjects;\n    if (selectedLevel === \"secondary\") return secondarySubjects;\n    if (selectedLevel === \"advance\") return advanceSubjects;\n    return [];\n  }, [selectedLevel]);\n\n  // Fetch videos\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      dispatch(ShowLoading());\n\n      const filters = {\n        level: selectedLevel,\n        className: \"\", // Get all classes for the level\n        subject: \"\", // Get all subjects for the level\n        content: \"videos\"\n      };\n\n      const response = await getStudyMaterial(filters);\n\n      if (response?.data?.success) {\n        setVideos(response.data.data || []);\n      } else {\n        setError(response?.data?.message || \"Failed to fetch videos\");\n        setVideos([]);\n      }\n    } catch (error) {\n      console.error(\"Error fetching videos:\", error);\n      setError(\"Failed to load videos. Please try again.\");\n      setVideos([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [selectedLevel, dispatch]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos;\n\n    // Apply level filter\n    filtered = filtered.filter(video => video.level === selectedLevel);\n\n    // Apply class filter\n    if (selectedClass !== \"all\") {\n      filtered = filtered.filter(video => video.className === selectedClass);\n    }\n\n    // Apply subject filter\n    if (selectedSubject !== \"all\") {\n      filtered = filtered.filter(video => video.subject === selectedSubject);\n    }\n\n    // Apply search filter\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(video =>\n        video.title?.toLowerCase().includes(searchLower) ||\n        video.subject?.toLowerCase().includes(searchLower) ||\n        video.topic?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Apply sorting\n    const sorted = [...filtered].sort((a, b) => {\n      switch (sortBy) {\n        case \"newest\":\n          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);\n        case \"oldest\":\n          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);\n        case \"title\":\n          return (a.title || \"\").localeCompare(b.title || \"\");\n        case \"subject\":\n          return (a.subject || \"\").localeCompare(b.subject || \"\");\n        default:\n          return 0;\n      }\n    });\n\n    return sorted;\n  }, [videos, searchTerm, sortBy, selectedLevel, selectedClass, selectedSubject]);\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedVideos[index];\n\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    if (videoRef) {\n      videoRef.pause();\n    }\n  };\n\n  const toggleVideoExpansion = () => {\n    setIsVideoExpanded(!isVideoExpanded);\n  };\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async (videoUrl) => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n          }\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.error('❌ Error getting signed URL:', error);\n        return videoUrl;\n      }\n    }\n\n    return videoUrl;\n  };\n\n  // Get thumbnail URL\n  const getThumbnailUrl = (video) => {\n    if (video.thumbnail) {\n      return video.thumbnail;\n    }\n    \n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      let videoId = video.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    \n    return '/api/placeholder/400/225';\n  };\n\n  // Effects\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  useEffect(() => {\n    if (user?.level) {\n      setSelectedLevel(user.level);\n    }\n    if (user?.class) {\n      setSelectedClass(user.class);\n    }\n  }, [user]);\n\n  // Clear search and refresh\n  const handleClearSearch = () => {\n    setSearchTerm(\"\");\n  };\n\n  const handleRefresh = () => {\n    // Only refresh data, don't clear filters or search\n    fetchVideos();\n  };\n\n  const handleClearAll = () => {\n    setSearchTerm(\"\");\n    setSelectedSubject(\"all\");\n    setSelectedClass(\"all\");\n    fetchVideos();\n  };\n\n  // Comment functions\n  const handleAddComment = () => {\n    if (newComment.trim()) {\n      const comment = {\n        id: Date.now(),\n        text: newComment,\n        author: user?.name || \"Anonymous\",\n        timestamp: new Date().toISOString(),\n        replies: []\n      };\n      setComments([...comments, comment]);\n      setNewComment(\"\");\n    }\n  };\n\n  const handleAddReply = (commentId) => {\n    if (replyText.trim()) {\n      const reply = {\n        id: Date.now(),\n        text: replyText,\n        author: user?.name || \"Anonymous\",\n        timestamp: new Date().toISOString()\n      };\n\n      setComments(comments.map(comment =>\n        comment.id === commentId\n          ? { ...comment, replies: [...comment.replies, reply] }\n          : comment\n      ));\n      setReplyText(\"\");\n      setReplyingTo(null);\n    }\n  };\n\n  return (\n    <div className=\"video-lessons-container\">\n      {/* Enhanced Header with Level Display */}\n      <div className=\"video-lessons-header\">\n        <div className=\"header-content\">\n          <div className=\"header-main\">\n            <div className=\"header-icon\">\n              <TbVideo />\n            </div>\n            <div className=\"header-text\">\n              <h1>Video Lessons</h1>\n              <p>Watch educational videos to enhance your learning</p>\n            </div>\n          </div>\n\n          {/* Level and Class Display */}\n          <div className=\"level-display\">\n            <div className=\"current-level\">\n              <span className=\"level-label\">Level:</span>\n              <span className=\"level-value\">{selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)}</span>\n            </div>\n            <div className=\"current-class\">\n              <span className=\"class-label\">Your Class:</span>\n              <span className=\"class-value\">\n                {user?.level === 'primary' ? `Class ${user?.class || 'N/A'}` :\n                 user?.level === 'secondary' ? `Form ${user?.class || 'N/A'}` :\n                 user?.level === 'advance' ? `Form ${user?.class || 'N/A'}` :\n                 'Not Set'}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"video-lessons-content\">\n        {/* Enhanced Filters and Controls */}\n        <div className=\"video-controls\">\n          <div className=\"controls-row\">\n            {/* Level Selector */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbSchool />\n                Level\n              </label>\n              <select\n                value={selectedLevel}\n                onChange={(e) => setSelectedLevel(e.target.value)}\n                className=\"control-select level-select\"\n              >\n                <option value=\"primary\">Primary (1-7)</option>\n                <option value=\"secondary\">Secondary (1-4)</option>\n                <option value=\"advance\">Advance (5-6)</option>\n              </select>\n            </div>\n\n            {/* Class Filter */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbFilter />\n                Filter by Class\n              </label>\n              <select\n                value={selectedClass}\n                onChange={(e) => setSelectedClass(e.target.value)}\n                className=\"control-select class-select\"\n              >\n                <option value=\"all\">All Classes</option>\n                {availableClasses.map((cls) => (\n                  <option key={cls} value={cls}>\n                    {selectedLevel === 'primary' ? `Class ${cls}` : `Form ${cls}`}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Subject Filter */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbFilter />\n                Subject\n              </label>\n              <select\n                value={selectedSubject}\n                onChange={(e) => setSelectedSubject(e.target.value)}\n                className=\"control-select subject-select\"\n              >\n                <option value=\"all\">All Subjects</option>\n                {availableSubjects.map((subject) => (\n                  <option key={subject} value={subject}>\n                    {subject}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Sort */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbSortAscending />\n                Sort\n              </label>\n              <select\n                value={sortBy}\n                onChange={(e) => setSortBy(e.target.value)}\n                className=\"control-select sort-select\"\n              >\n                <option value=\"newest\">Newest First</option>\n                <option value=\"oldest\">Oldest First</option>\n                <option value=\"title\">Title A-Z</option>\n                <option value=\"subject\">Subject A-Z</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Search Row */}\n          <div className=\"search-row\">\n            <div className=\"search-container\">\n              <TbSearch className=\"search-icon\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search videos by title, subject, or topic...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"search-input\"\n              />\n              {searchTerm && (\n                <button onClick={handleClearSearch} className=\"clear-search-btn\">\n                  <TbX />\n                  Clear Search\n                </button>\n              )}\n            </div>\n\n            <button onClick={handleRefresh} className=\"refresh-btn\">\n              <TbDownload />\n              Refresh All\n            </button>\n          </div>\n        </div>\n\n        {/* Loading State */}\n        {loading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>Loading videos...</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <TbAlertTriangle className=\"error-icon\" />\n            <h3>Error Loading Videos</h3>\n            <p>{error}</p>\n            <button onClick={fetchVideos} className=\"retry-btn\">\n              Try Again\n            </button>\n          </div>\n        ) : filteredAndSortedVideos.length > 0 ? (\n          <div className=\"videos-grid\">\n            {filteredAndSortedVideos.map((video, index) => (\n              <div key={index} className=\"video-card\" onClick={() => handleShowVideo(index)}>\n                <div className=\"video-card-thumbnail\">\n                  <img\n                    src={getThumbnailUrl(video)}\n                    alt={video.title}\n                    className=\"thumbnail-image\"\n                    onError={(e) => {\n                      // Fallback logic for failed thumbnails\n                      if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                        // For YouTube videos, try different quality thumbnails\n                        let videoId = video.videoID;\n                        if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                          const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                          videoId = match ? match[1] : videoId;\n                        }\n\n                        const fallbacks = [\n                          `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n                          `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,\n                          `https://img.youtube.com/vi/${videoId}/default.jpg`,\n                          '/api/placeholder/320/180'\n                        ];\n\n                        const currentSrc = e.target.src;\n                        const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n\n                        if (currentIndex < fallbacks.length - 1) {\n                          e.target.src = fallbacks[currentIndex + 1];\n                        }\n                      } else {\n                        e.target.src = '/api/placeholder/320/180';\n                      }\n                    }}\n                  />\n                  <div className=\"play-overlay\">\n                    <FaPlayCircle className=\"play-icon\" />\n                  </div>\n                  <div className=\"video-duration\">\n                    {video.duration || \"Video\"}\n                  </div>\n                  {video.subtitles && video.subtitles.length > 0 && (\n                    <div className=\"subtitle-badge\">\n                      <TbInfoCircle />\n                      CC\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"video-card-content\">\n                  <h3 className=\"video-title\">{video.title}</h3>\n                  <div className=\"video-meta\">\n                    <span className=\"video-subject\">{video.subject}</span>\n                    <span className=\"video-class\">\n                      {selectedLevel === 'primary' ? `Class ${video.className}` : `Form ${video.className}`}\n                    </span>\n                  </div>\n                  <div className=\"video-tags\">\n                    <span className=\"level-tag\">{video.level}</span>\n                    {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n                    {video.sharedFromClass && video.sharedFromClass !== video.className && (\n                      <span className=\"shared-tag\">\n                        Shared from {selectedLevel === 'primary' ? `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`}\n                      </span>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>No Videos Found</h3>\n            <p>No video lessons are available for your current selection.</p>\n            <p className=\"suggestion\">Try selecting a different class or subject.</p>\n          </div>\n        )}\n      </div>\n\n      {/* Enhanced Video Display */}\n      {showVideoIndices.length > 0 && currentVideoIndex !== null && (\n        <div className={`video-overlay ${isVideoExpanded ? 'expanded' : ''}`} onClick={(e) => {\n          if (e.target === e.currentTarget) handleHideVideo();\n        }}>\n          <div className={`video-modal ${isVideoExpanded ? 'expanded' : ''}`}>\n            {(() => {\n              const video = filteredAndSortedVideos[currentVideoIndex];\n              if (!video) return <div>Video not found</div>;\n\n              return (\n                <div className=\"video-content\">\n                  <div className=\"video-header\">\n                    <div className=\"video-info\">\n                      <h3 className=\"video-title\">{video.title}</h3>\n                      <div className=\"video-meta\">\n                        <span className=\"video-subject\">{video.subject}</span>\n                        <span className=\"video-class\">Class {video.className}</span>\n                        {video.level && <span className=\"video-level\">{video.level}</span>}\n                      </div>\n                    </div>\n                    <div className=\"video-controls\">\n                      <button\n                        className=\"control-btn expand-btn\"\n                        onClick={toggleVideoExpansion}\n                        title={isVideoExpanded ? \"Exit fullscreen\" : \"Enter fullscreen\"}\n                      >\n                        {isVideoExpanded ? <FaCompress /> : <FaExpand />}\n                      </button>\n                      <button\n                        className=\"control-btn close-btn\"\n                        onClick={handleHideVideo}\n                        title=\"Close video\"\n                      >\n                        <FaTimes />\n                      </button>\n                    </div>\n                  </div>\n                  <div className=\"video-container\">\n                    {video.videoUrl ? (\n                      <div style={{ padding: '15px', background: '#000', borderRadius: '8px' }}>\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"400\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '400px',\n                              backgroundColor: '#000'\n                            }}\n                            onError={(e) => {\n                              setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                            }}\n                            onCanPlay={() => {\n                              setVideoError(null);\n                            }}\n                            onLoadStart={() => {\n                              console.log('🎬 Video loading started');\n                            }}\n                            crossOrigin=\"anonymous\"\n                          >\n                            {/* Use signed URL if available, otherwise use original URL */}\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n\n                            {/* Add subtitle tracks if available */}\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n\n                            Your browser does not support the video tag.\n                          </video>\n\n                          {/* Subtitle indicator */}\n                          {video.subtitles && video.subtitles.length > 0 && (\n                            <div className=\"subtitle-indicator\">\n                              <TbInfoCircle className=\"subtitle-icon\" />\n                              <span>Subtitles available in {video.subtitles.length} language(s)</span>\n                            </div>\n                          )}\n\n                          {/* Video error display */}\n                          {videoError && (\n                            <div className=\"video-error-overlay\">\n                              <div className=\"error-content\">\n                                <TbAlertTriangle className=\"error-icon\" />\n                                <p>{videoError}</p>\n                                <button onClick={() => setVideoError(null)} className=\"dismiss-error-btn\">\n                                  Dismiss\n                                </button>\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                    ) : video.videoID ? (\n                      // Fallback to YouTube embed if no videoUrl\n                      <iframe\n                        src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                        title={video.title}\n                        frameBorder=\"0\"\n                        allowFullScreen\n                        className=\"video-iframe\"\n                        onLoad={() => console.log('✅ YouTube iframe loaded')}\n                      ></iframe>\n                    ) : (\n                      <div className=\"video-error\">\n                        <div className=\"error-icon\">⚠️</div>\n                        <h3>Video Unavailable</h3>\n                        <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                        <div className=\"error-actions\">\n                          <a\n                            href={video.signedVideoUrl || video.videoUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"external-link-btn\"\n                          >\n                            📱 Open in New Tab\n                          </a>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Comments Section */}\n                  {!isVideoExpanded && (\n                    <div className=\"comments-section\">\n                      <h4 className=\"comments-title\">\n                        <TbInfoCircle />\n                        Discussion ({comments.length})\n                      </h4>\n\n                      {/* Add Comment */}\n                      <div className=\"add-comment\">\n                        <div className=\"comment-input-container\">\n                          <textarea\n                            value={newComment}\n                            onChange={(e) => setNewComment(e.target.value)}\n                            placeholder=\"Share your thoughts about this video...\"\n                            className=\"comment-input\"\n                            rows=\"3\"\n                          />\n                          <button\n                            onClick={handleAddComment}\n                            className=\"comment-submit-btn\"\n                            disabled={!newComment.trim()}\n                          >\n                            Post Comment\n                          </button>\n                        </div>\n                      </div>\n\n                      {/* Comments List */}\n                      <div className=\"comments-list\">\n                        {comments.length === 0 ? (\n                          <div className=\"no-comments\">\n                            <TbInfoCircle />\n                            <p>No comments yet. Be the first to share your thoughts!</p>\n                          </div>\n                        ) : (\n                          comments.map((comment) => (\n                            <div key={comment.id} className=\"comment\">\n                              <div className=\"comment-header\">\n                                <span className=\"comment-author\">{comment.author}</span>\n                                <span className=\"comment-time\">\n                                  {new Date(comment.timestamp).toLocaleDateString()}\n                                </span>\n                              </div>\n                              <div className=\"comment-text\">{comment.text}</div>\n                              <div className=\"comment-actions\">\n                                <button\n                                  onClick={() => setReplyingTo(comment.id)}\n                                  className=\"reply-btn\"\n                                >\n                                  Reply\n                                </button>\n                              </div>\n\n                              {/* Reply Input */}\n                              {replyingTo === comment.id && (\n                                <div className=\"reply-input-container\">\n                                  <textarea\n                                    value={replyText}\n                                    onChange={(e) => setReplyText(e.target.value)}\n                                    placeholder=\"Write a reply...\"\n                                    className=\"reply-input\"\n                                    rows=\"2\"\n                                  />\n                                  <div className=\"reply-actions\">\n                                    <button\n                                      onClick={() => handleAddReply(comment.id)}\n                                      className=\"reply-submit-btn\"\n                                      disabled={!replyText.trim()}\n                                    >\n                                      Reply\n                                    </button>\n                                    <button\n                                      onClick={() => {\n                                        setReplyingTo(null);\n                                        setReplyText(\"\");\n                                      }}\n                                      className=\"reply-cancel-btn\"\n                                    >\n                                      Cancel\n                                    </button>\n                                  </div>\n                                </div>\n                              )}\n\n                              {/* Replies */}\n                              {comment.replies.length > 0 && (\n                                <div className=\"replies\">\n                                  {comment.replies.map((reply) => (\n                                    <div key={reply.id} className=\"reply\">\n                                      <div className=\"reply-header\">\n                                        <span className=\"reply-author\">{reply.author}</span>\n                                        <span className=\"reply-time\">\n                                          {new Date(reply.timestamp).toLocaleDateString()}\n                                        </span>\n                                      </div>\n                                      <div className=\"reply-text\">{reply.text}</div>\n                                    </div>\n                                  ))}\n                                </div>\n                              )}\n                            </div>\n                          ))\n                        )}\n                      </div>\n                    </div>\n                  )}\n                </div>\n              );\n            })()}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default VideoLessons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,OAAO,aAAa;AACpB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AAErE,SACEC,YAAY,EACZC,OAAO,EACPC,eAAe,EACfC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,aAAa,EACbC,QAAQ,EACRC,QAAQ,EACRC,UAAU,QACL,gBAAgB;AACvB,SACEC,OAAO,EACPC,QAAQ,EACRC,QAAQ,EACRC,QAAQ,EACRC,eAAe,EACfC,UAAU,EACVC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,aAAa,IAAIC,iBAAiB,EAClCC,WAAW,EACXC,GAAG,EACHC,eAAe,EACfC,YAAY,EACZC,OAAO,QACF,gBAAgB;AACvB,SAASC,eAAe,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjG,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGpC,WAAW,CAAEqC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAME,QAAQ,GAAGvC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACwC,MAAM,EAAEC,SAAS,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmD,KAAK,EAAEC,QAAQ,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqD,aAAa,EAAEC,gBAAgB,CAAC,GAAGtD,QAAQ,CAAC,CAAA4C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,KAAK,KAAI,SAAS,CAAC;EAC5E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC0D,eAAe,EAAEC,kBAAkB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4D,UAAU,EAAEC,aAAa,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8D,MAAM,EAAEC,SAAS,CAAC,GAAG/D,QAAQ,CAAC,QAAQ,CAAC;;EAE9C;EACA,MAAM,CAACgE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACkE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACoE,eAAe,EAAEC,kBAAkB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACwE,QAAQ,EAAEC,WAAW,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;;EAE9C;EACA,MAAM,CAAC0E,QAAQ,EAAEC,WAAW,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4E,UAAU,EAAEC,aAAa,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8E,UAAU,EAAEC,aAAa,CAAC,GAAG/E,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACgF,SAAS,EAAEC,YAAY,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAMkF,gBAAgB,GAAG/E,OAAO,CAAC,MAAM;IACrC,IAAIkD,aAAa,KAAK,SAAS,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC3E,IAAIA,aAAa,KAAK,WAAW,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC9D,IAAIA,aAAa,KAAK,SAAS,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IAClD,OAAO,EAAE;EACX,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAM8B,iBAAiB,GAAGhF,OAAO,CAAC,MAAM;IACtC,IAAIkD,aAAa,KAAK,SAAS,EAAE,OAAOhB,eAAe;IACvD,IAAIgB,aAAa,KAAK,WAAW,EAAE,OAAOf,iBAAiB;IAC3D,IAAIe,aAAa,KAAK,SAAS,EAAE,OAAOd,eAAe;IACvD,OAAO,EAAE;EACX,CAAC,EAAE,CAACc,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAM+B,WAAW,GAAGlF,WAAW,CAAC,YAAY;IAC1C,IAAI;MAAA,IAAAmF,cAAA;MACFnC,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACdN,QAAQ,CAACpC,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAM4E,OAAO,GAAG;QACd/B,KAAK,EAAEF,aAAa;QACpBkC,SAAS,EAAE,EAAE;QAAE;QACfC,OAAO,EAAE,EAAE;QAAE;QACbC,OAAO,EAAE;MACX,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAMpF,gBAAgB,CAACgF,OAAO,CAAC;MAEhD,IAAII,QAAQ,aAARA,QAAQ,gBAAAL,cAAA,GAARK,QAAQ,CAAEC,IAAI,cAAAN,cAAA,eAAdA,cAAA,CAAgBO,OAAO,EAAE;QAC3B5C,SAAS,CAAC0C,QAAQ,CAACC,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MACrC,CAAC,MAAM;QAAA,IAAAE,eAAA;QACLzC,QAAQ,CAAC,CAAAsC,QAAQ,aAARA,QAAQ,wBAAAG,eAAA,GAARH,QAAQ,CAAEC,IAAI,cAAAE,eAAA,uBAAdA,eAAA,CAAgBC,OAAO,KAAI,wBAAwB,CAAC;QAC7D9C,SAAS,CAAC,EAAE,CAAC;MACf;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd4C,OAAO,CAAC5C,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CC,QAAQ,CAAC,0CAA0C,CAAC;MACpDJ,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;MACjBJ,QAAQ,CAACrC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAAC4C,aAAa,EAAEP,QAAQ,CAAC,CAAC;;EAE7B;EACA,MAAMkD,uBAAuB,GAAG7F,OAAO,CAAC,MAAM;IAC5C,IAAI8F,QAAQ,GAAGlD,MAAM;;IAErB;IACAkD,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAC5C,KAAK,KAAKF,aAAa,CAAC;;IAElE;IACA,IAAIG,aAAa,KAAK,KAAK,EAAE;MAC3ByC,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACZ,SAAS,KAAK/B,aAAa,CAAC;IACxE;;IAEA;IACA,IAAIE,eAAe,KAAK,KAAK,EAAE;MAC7BuC,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACX,OAAO,KAAK9B,eAAe,CAAC;IACxE;;IAEA;IACA,IAAIE,UAAU,CAACwC,IAAI,CAAC,CAAC,EAAE;MACrB,MAAMC,WAAW,GAAGzC,UAAU,CAAC0C,WAAW,CAAC,CAAC;MAC5CL,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK;QAAA,IAAAI,YAAA,EAAAC,cAAA,EAAAC,YAAA;QAAA,OAC9B,EAAAF,YAAA,GAAAJ,KAAK,CAACO,KAAK,cAAAH,YAAA,uBAAXA,YAAA,CAAaD,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,WAAW,CAAC,OAAAG,cAAA,GAChDL,KAAK,CAACX,OAAO,cAAAgB,cAAA,uBAAbA,cAAA,CAAeF,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,WAAW,CAAC,OAAAI,YAAA,GAClDN,KAAK,CAACS,KAAK,cAAAH,YAAA,uBAAXA,YAAA,CAAaH,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,WAAW,CAAC;MAAA,CAClD,CAAC;IACH;;IAEA;IACA,MAAMQ,MAAM,GAAG,CAAC,GAAGZ,QAAQ,CAAC,CAACa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC1C,QAAQlD,MAAM;QACZ,KAAK,QAAQ;UACX,OAAO,IAAImD,IAAI,CAACD,CAAC,CAACE,SAAS,IAAI,CAAC,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,IAAI,CAAC,CAAC;QAChE,KAAK,QAAQ;UACX,OAAO,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,IAAI,CAAC,CAAC,GAAG,IAAID,IAAI,CAACD,CAAC,CAACE,SAAS,IAAI,CAAC,CAAC;QAChE,KAAK,OAAO;UACV,OAAO,CAACH,CAAC,CAACL,KAAK,IAAI,EAAE,EAAES,aAAa,CAACH,CAAC,CAACN,KAAK,IAAI,EAAE,CAAC;QACrD,KAAK,SAAS;UACZ,OAAO,CAACK,CAAC,CAACvB,OAAO,IAAI,EAAE,EAAE2B,aAAa,CAACH,CAAC,CAACxB,OAAO,IAAI,EAAE,CAAC;QACzD;UACE,OAAO,CAAC;MACZ;IACF,CAAC,CAAC;IAEF,OAAOqB,MAAM;EACf,CAAC,EAAE,CAAC9D,MAAM,EAAEa,UAAU,EAAEE,MAAM,EAAET,aAAa,EAAEG,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAE/E;EACA,MAAM0D,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,MAAMlB,KAAK,GAAGH,uBAAuB,CAACqB,KAAK,CAAC;IAE5CpD,oBAAoB,CAACoD,KAAK,CAAC;IAC3BlD,mBAAmB,CAAC,CAACkD,KAAK,CAAC,CAAC;IAC5BhD,kBAAkB,CAAC,KAAK,CAAC;IACzBE,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,IAAI4B,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEmB,QAAQ,KAAKnB,KAAK,CAACmB,QAAQ,CAACX,QAAQ,CAAC,eAAe,CAAC,IAAIR,KAAK,CAACmB,QAAQ,CAACX,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,IAAI;QACF,MAAMY,SAAS,GAAG,MAAMC,iBAAiB,CAACrB,KAAK,CAACmB,QAAQ,CAAC;QACzDnB,KAAK,CAACsB,cAAc,GAAGF,SAAS;MAClC,CAAC,CAAC,OAAOpE,KAAK,EAAE;QACd4C,OAAO,CAAC2B,IAAI,CAAC,8CAA8C,CAAC;QAC5DvB,KAAK,CAACsB,cAAc,GAAGtB,KAAK,CAACmB,QAAQ;MACvC;IACF;EACF,CAAC;EAED,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5BxD,mBAAmB,CAAC,EAAE,CAAC;IACvBF,oBAAoB,CAAC,IAAI,CAAC;IAC1BI,kBAAkB,CAAC,KAAK,CAAC;IACzBE,aAAa,CAAC,IAAI,CAAC;IACnB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAACoD,KAAK,CAAC,CAAC;IAClB;EACF,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjCxD,kBAAkB,CAAC,CAACD,eAAe,CAAC;EACtC,CAAC;;EAED;EACA,MAAMoD,iBAAiB,GAAG,MAAOF,QAAQ,IAAK;IAC5C,IAAI,CAACA,QAAQ,EAAE,OAAOA,QAAQ;;IAE9B;IACA,IAAIA,QAAQ,CAACX,QAAQ,CAAC,eAAe,CAAC,IAAIW,QAAQ,CAACX,QAAQ,CAAC,KAAK,CAAC,EAAE;MAClE,IAAI;QACF,MAAMjB,QAAQ,GAAG,MAAMoC,KAAK,CAAE,6DAA4DC,kBAAkB,CAACT,QAAQ,CAAE,EAAC,EAAE;UACxHU,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAI,CAACvC,QAAQ,CAACwC,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAE,uBAAsBzC,QAAQ,CAAC0C,MAAO,EAAC,CAAC;QAC3D;QAEA,MAAMzC,IAAI,GAAG,MAAMD,QAAQ,CAAC2C,IAAI,CAAC,CAAC;QAElC,IAAI1C,IAAI,CAACC,OAAO,IAAID,IAAI,CAAC4B,SAAS,EAAE;UAClCxB,OAAO,CAACuC,GAAG,CAAC,+BAA+B,CAAC;UAC5C,OAAO3C,IAAI,CAAC4B,SAAS;QACvB,CAAC,MAAM;UACLxB,OAAO,CAAC2B,IAAI,CAAC,+CAA+C,EAAE/B,IAAI,CAAC;UACnE,OAAO2B,QAAQ;QACjB;MACF,CAAC,CAAC,OAAOnE,KAAK,EAAE;QACd4C,OAAO,CAAC5C,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,OAAOmE,QAAQ;MACjB;IACF;IAEA,OAAOA,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMiB,eAAe,GAAIpC,KAAK,IAAK;IACjC,IAAIA,KAAK,CAACqC,SAAS,EAAE;MACnB,OAAOrC,KAAK,CAACqC,SAAS;IACxB;IAEA,IAAIrC,KAAK,CAACsC,OAAO,IAAI,CAACtC,KAAK,CAACsC,OAAO,CAAC9B,QAAQ,CAAC,eAAe,CAAC,EAAE;MAC7D,IAAI+B,OAAO,GAAGvC,KAAK,CAACsC,OAAO;MAC3B,IAAIC,OAAO,CAAC/B,QAAQ,CAAC,aAAa,CAAC,IAAI+B,OAAO,CAAC/B,QAAQ,CAAC,UAAU,CAAC,EAAE;QACnE,MAAMgC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;QACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;MACtC;MACA,OAAQ,8BAA6BA,OAAQ,oBAAmB;IAClE;IAEA,OAAO,0BAA0B;EACnC,CAAC;;EAED;EACAzI,SAAS,CAAC,MAAM;IACdmF,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAEjBnF,SAAS,CAAC,MAAM;IACd,IAAI2C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEW,KAAK,EAAE;MACfD,gBAAgB,CAACV,IAAI,CAACW,KAAK,CAAC;IAC9B;IACA,IAAIX,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgG,KAAK,EAAE;MACfnF,gBAAgB,CAACb,IAAI,CAACgG,KAAK,CAAC;IAC9B;EACF,CAAC,EAAE,CAAChG,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMiG,iBAAiB,GAAGA,CAAA,KAAM;IAC9BhF,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;EAED,MAAMiF,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA1D,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAM2D,cAAc,GAAGA,CAAA,KAAM;IAC3BlF,aAAa,CAAC,EAAE,CAAC;IACjBF,kBAAkB,CAAC,KAAK,CAAC;IACzBF,gBAAgB,CAAC,KAAK,CAAC;IACvB2B,WAAW,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAM4D,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIpE,UAAU,CAACwB,IAAI,CAAC,CAAC,EAAE;MACrB,MAAM6C,OAAO,GAAG;QACdC,EAAE,EAAEjC,IAAI,CAACkC,GAAG,CAAC,CAAC;QACdC,IAAI,EAAExE,UAAU;QAChByE,MAAM,EAAE,CAAAzG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0G,IAAI,KAAI,WAAW;QACjCC,SAAS,EAAE,IAAItC,IAAI,CAAC,CAAC,CAACuC,WAAW,CAAC,CAAC;QACnCC,OAAO,EAAE;MACX,CAAC;MACD9E,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAEuE,OAAO,CAAC,CAAC;MACnCpE,aAAa,CAAC,EAAE,CAAC;IACnB;EACF,CAAC;EAED,MAAM6E,cAAc,GAAIC,SAAS,IAAK;IACpC,IAAI3E,SAAS,CAACoB,IAAI,CAAC,CAAC,EAAE;MACpB,MAAMwD,KAAK,GAAG;QACZV,EAAE,EAAEjC,IAAI,CAACkC,GAAG,CAAC,CAAC;QACdC,IAAI,EAAEpE,SAAS;QACfqE,MAAM,EAAE,CAAAzG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0G,IAAI,KAAI,WAAW;QACjCC,SAAS,EAAE,IAAItC,IAAI,CAAC,CAAC,CAACuC,WAAW,CAAC;MACpC,CAAC;MAED7E,WAAW,CAACD,QAAQ,CAACmF,GAAG,CAACZ,OAAO,IAC9BA,OAAO,CAACC,EAAE,KAAKS,SAAS,GACpB;QAAE,GAAGV,OAAO;QAAEQ,OAAO,EAAE,CAAC,GAAGR,OAAO,CAACQ,OAAO,EAAEG,KAAK;MAAE,CAAC,GACpDX,OACN,CAAC,CAAC;MACFhE,YAAY,CAAC,EAAE,CAAC;MAChBF,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,oBACEtC,OAAA;IAAK8C,SAAS,EAAC,yBAAyB;IAAAuE,QAAA,gBAEtCrH,OAAA;MAAK8C,SAAS,EAAC,sBAAsB;MAAAuE,QAAA,eACnCrH,OAAA;QAAK8C,SAAS,EAAC,gBAAgB;QAAAuE,QAAA,gBAC7BrH,OAAA;UAAK8C,SAAS,EAAC,aAAa;UAAAuE,QAAA,gBAC1BrH,OAAA;YAAK8C,SAAS,EAAC,aAAa;YAAAuE,QAAA,eAC1BrH,OAAA,CAACpB,OAAO;cAAA0I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACNzH,OAAA;YAAK8C,SAAS,EAAC,aAAa;YAAAuE,QAAA,gBAC1BrH,OAAA;cAAAqH,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBzH,OAAA;cAAAqH,QAAA,EAAG;YAAiD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzH,OAAA;UAAK8C,SAAS,EAAC,eAAe;UAAAuE,QAAA,gBAC5BrH,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAAuE,QAAA,gBAC5BrH,OAAA;cAAM8C,SAAS,EAAC,aAAa;cAAAuE,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3CzH,OAAA;cAAM8C,SAAS,EAAC,aAAa;cAAAuE,QAAA,EAAEzG,aAAa,CAAC8G,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG/G,aAAa,CAACgH,KAAK,CAAC,CAAC;YAAC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC,eACNzH,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAAuE,QAAA,gBAC5BrH,OAAA;cAAM8C,SAAS,EAAC,aAAa;cAAAuE,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDzH,OAAA;cAAM8C,SAAS,EAAC,aAAa;cAAAuE,QAAA,EAC1B,CAAAlH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,KAAK,MAAK,SAAS,GAAI,SAAQ,CAAAX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgG,KAAK,KAAI,KAAM,EAAC,GAC3D,CAAAhG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,KAAK,MAAK,WAAW,GAAI,QAAO,CAAAX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgG,KAAK,KAAI,KAAM,EAAC,GAC5D,CAAAhG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,KAAK,MAAK,SAAS,GAAI,QAAO,CAAAX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgG,KAAK,KAAI,KAAM,EAAC,GAC1D;YAAS;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENzH,OAAA;MAAK8C,SAAS,EAAC,uBAAuB;MAAAuE,QAAA,gBAEpCrH,OAAA;QAAK8C,SAAS,EAAC,gBAAgB;QAAAuE,QAAA,gBAC7BrH,OAAA;UAAK8C,SAAS,EAAC,cAAc;UAAAuE,QAAA,gBAE3BrH,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAAuE,QAAA,gBAC5BrH,OAAA;cAAO8C,SAAS,EAAC,eAAe;cAAAuE,QAAA,gBAC9BrH,OAAA,CAACnB,QAAQ;gBAAAyI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,SAEd;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzH,OAAA;cACE6H,KAAK,EAAEjH,aAAc;cACrBkH,QAAQ,EAAGC,CAAC,IAAKlH,gBAAgB,CAACkH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAClD/E,SAAS,EAAC,6BAA6B;cAAAuE,QAAA,gBAEvCrH,OAAA;gBAAQ6H,KAAK,EAAC,SAAS;gBAAAR,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9CzH,OAAA;gBAAQ6H,KAAK,EAAC,WAAW;gBAAAR,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClDzH,OAAA;gBAAQ6H,KAAK,EAAC,SAAS;gBAAAR,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNzH,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAAuE,QAAA,gBAC5BrH,OAAA;cAAO8C,SAAS,EAAC,eAAe;cAAAuE,QAAA,gBAC9BrH,OAAA,CAACjB,QAAQ;gBAAAuI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAEd;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzH,OAAA;cACE6H,KAAK,EAAE9G,aAAc;cACrB+G,QAAQ,EAAGC,CAAC,IAAK/G,gBAAgB,CAAC+G,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAClD/E,SAAS,EAAC,6BAA6B;cAAAuE,QAAA,gBAEvCrH,OAAA;gBAAQ6H,KAAK,EAAC,KAAK;gBAAAR,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACvChF,gBAAgB,CAAC2E,GAAG,CAAEa,GAAG,iBACxBjI,OAAA;gBAAkB6H,KAAK,EAAEI,GAAI;gBAAAZ,QAAA,EAC1BzG,aAAa,KAAK,SAAS,GAAI,SAAQqH,GAAI,EAAC,GAAI,QAAOA,GAAI;cAAC,GADlDA,GAAG;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAER,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNzH,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAAuE,QAAA,gBAC5BrH,OAAA;cAAO8C,SAAS,EAAC,eAAe;cAAAuE,QAAA,gBAC9BrH,OAAA,CAACjB,QAAQ;gBAAAuI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEd;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzH,OAAA;cACE6H,KAAK,EAAE5G,eAAgB;cACvB6G,QAAQ,EAAGC,CAAC,IAAK7G,kBAAkB,CAAC6G,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACpD/E,SAAS,EAAC,+BAA+B;cAAAuE,QAAA,gBAEzCrH,OAAA;gBAAQ6H,KAAK,EAAC,KAAK;gBAAAR,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACxC/E,iBAAiB,CAAC0E,GAAG,CAAErE,OAAO,iBAC7B/C,OAAA;gBAAsB6H,KAAK,EAAE9E,OAAQ;gBAAAsE,QAAA,EAClCtE;cAAO,GADGA,OAAO;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEZ,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNzH,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAAuE,QAAA,gBAC5BrH,OAAA;cAAO8C,SAAS,EAAC,eAAe;cAAAuE,QAAA,gBAC9BrH,OAAA,CAAChB,eAAe;gBAAAsI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,QAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzH,OAAA;cACE6H,KAAK,EAAExG,MAAO;cACdyG,QAAQ,EAAGC,CAAC,IAAKzG,SAAS,CAACyG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC3C/E,SAAS,EAAC,4BAA4B;cAAAuE,QAAA,gBAEtCrH,OAAA;gBAAQ6H,KAAK,EAAC,QAAQ;gBAAAR,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CzH,OAAA;gBAAQ6H,KAAK,EAAC,QAAQ;gBAAAR,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CzH,OAAA;gBAAQ6H,KAAK,EAAC,OAAO;gBAAAR,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCzH,OAAA;gBAAQ6H,KAAK,EAAC,SAAS;gBAAAR,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzH,OAAA;UAAK8C,SAAS,EAAC,YAAY;UAAAuE,QAAA,gBACzBrH,OAAA;YAAK8C,SAAS,EAAC,kBAAkB;YAAAuE,QAAA,gBAC/BrH,OAAA,CAAClB,QAAQ;cAACgE,SAAS,EAAC;YAAa;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpCzH,OAAA;cACEkI,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,8CAA8C;cAC1DN,KAAK,EAAE1G,UAAW;cAClB2G,QAAQ,EAAGC,CAAC,IAAK3G,aAAa,CAAC2G,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/C/E,SAAS,EAAC;YAAc;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,EACDtG,UAAU,iBACTnB,OAAA;cAAQoI,OAAO,EAAEhC,iBAAkB;cAACtD,SAAS,EAAC,kBAAkB;cAAAuE,QAAA,gBAC9DrH,OAAA,CAACR,GAAG;gBAAA8H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAET;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENzH,OAAA;YAAQoI,OAAO,EAAE/B,aAAc;YAACvD,SAAS,EAAC,aAAa;YAAAuE,QAAA,gBACrDrH,OAAA,CAACf,UAAU;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEhB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLjH,OAAO,gBACNR,OAAA;QAAK8C,SAAS,EAAC,eAAe;QAAAuE,QAAA,gBAC5BrH,OAAA;UAAK8C,SAAS,EAAC;QAAiB;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCzH,OAAA;UAAAqH,QAAA,EAAG;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,GACJ/G,KAAK,gBACPV,OAAA;QAAK8C,SAAS,EAAC,aAAa;QAAAuE,QAAA,gBAC1BrH,OAAA,CAACP,eAAe;UAACqD,SAAS,EAAC;QAAY;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CzH,OAAA;UAAAqH,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BzH,OAAA;UAAAqH,QAAA,EAAI3G;QAAK;UAAA4G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdzH,OAAA;UAAQoI,OAAO,EAAEzF,WAAY;UAACG,SAAS,EAAC,WAAW;UAAAuE,QAAA,EAAC;QAEpD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJlE,uBAAuB,CAAC8E,MAAM,GAAG,CAAC,gBACpCrI,OAAA;QAAK8C,SAAS,EAAC,aAAa;QAAAuE,QAAA,EACzB9D,uBAAuB,CAAC6D,GAAG,CAAC,CAAC1D,KAAK,EAAEkB,KAAK,kBACxC5E,OAAA;UAAiB8C,SAAS,EAAC,YAAY;UAACsF,OAAO,EAAEA,CAAA,KAAMzD,eAAe,CAACC,KAAK,CAAE;UAAAyC,QAAA,gBAC5ErH,OAAA;YAAK8C,SAAS,EAAC,sBAAsB;YAAAuE,QAAA,gBACnCrH,OAAA;cACEsI,GAAG,EAAExC,eAAe,CAACpC,KAAK,CAAE;cAC5B6E,GAAG,EAAE7E,KAAK,CAACO,KAAM;cACjBnB,SAAS,EAAC,iBAAiB;cAC3B0F,OAAO,EAAGT,CAAC,IAAK;gBACd;gBACA,IAAIrE,KAAK,CAACsC,OAAO,IAAI,CAACtC,KAAK,CAACsC,OAAO,CAAC9B,QAAQ,CAAC,eAAe,CAAC,EAAE;kBAC7D;kBACA,IAAI+B,OAAO,GAAGvC,KAAK,CAACsC,OAAO;kBAC3B,IAAIC,OAAO,CAAC/B,QAAQ,CAAC,aAAa,CAAC,IAAI+B,OAAO,CAAC/B,QAAQ,CAAC,UAAU,CAAC,EAAE;oBACnE,MAAMgC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;oBACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;kBACtC;kBAEA,MAAMwC,SAAS,GAAG,CACf,8BAA6BxC,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,cAAa,EACnD,0BAA0B,CAC3B;kBAED,MAAMyC,UAAU,GAAGX,CAAC,CAACC,MAAM,CAACM,GAAG;kBAC/B,MAAMK,YAAY,GAAGF,SAAS,CAACG,SAAS,CAACC,GAAG,IAAIH,UAAU,CAACxE,QAAQ,CAAC2E,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;kBAE1F,IAAIJ,YAAY,GAAGF,SAAS,CAACJ,MAAM,GAAG,CAAC,EAAE;oBACvCN,CAAC,CAACC,MAAM,CAACM,GAAG,GAAGG,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;kBAC5C;gBACF,CAAC,MAAM;kBACLZ,CAAC,CAACC,MAAM,CAACM,GAAG,GAAG,0BAA0B;gBAC3C;cACF;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFzH,OAAA;cAAK8C,SAAS,EAAC,cAAc;cAAAuE,QAAA,eAC3BrH,OAAA,CAAC9B,YAAY;gBAAC4E,SAAS,EAAC;cAAW;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACNzH,OAAA;cAAK8C,SAAS,EAAC,gBAAgB;cAAAuE,QAAA,EAC5B3D,KAAK,CAACsF,QAAQ,IAAI;YAAO;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,EACL/D,KAAK,CAACuF,SAAS,IAAIvF,KAAK,CAACuF,SAAS,CAACZ,MAAM,GAAG,CAAC,iBAC5CrI,OAAA;cAAK8C,SAAS,EAAC,gBAAgB;cAAAuE,QAAA,gBAC7BrH,OAAA,CAACN,YAAY;gBAAA4H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,MAElB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENzH,OAAA;YAAK8C,SAAS,EAAC,oBAAoB;YAAAuE,QAAA,gBACjCrH,OAAA;cAAI8C,SAAS,EAAC,aAAa;cAAAuE,QAAA,EAAE3D,KAAK,CAACO;YAAK;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9CzH,OAAA;cAAK8C,SAAS,EAAC,YAAY;cAAAuE,QAAA,gBACzBrH,OAAA;gBAAM8C,SAAS,EAAC,eAAe;gBAAAuE,QAAA,EAAE3D,KAAK,CAACX;cAAO;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtDzH,OAAA;gBAAM8C,SAAS,EAAC,aAAa;gBAAAuE,QAAA,EAC1BzG,aAAa,KAAK,SAAS,GAAI,SAAQ8C,KAAK,CAACZ,SAAU,EAAC,GAAI,QAAOY,KAAK,CAACZ,SAAU;cAAC;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNzH,OAAA;cAAK8C,SAAS,EAAC,YAAY;cAAAuE,QAAA,gBACzBrH,OAAA;gBAAM8C,SAAS,EAAC,WAAW;gBAAAuE,QAAA,EAAE3D,KAAK,CAAC5C;cAAK;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC/C/D,KAAK,CAACS,KAAK,iBAAInE,OAAA;gBAAM8C,SAAS,EAAC,WAAW;gBAAAuE,QAAA,EAAE3D,KAAK,CAACS;cAAK;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC/D/D,KAAK,CAACwF,eAAe,IAAIxF,KAAK,CAACwF,eAAe,KAAKxF,KAAK,CAACZ,SAAS,iBACjE9C,OAAA;gBAAM8C,SAAS,EAAC,YAAY;gBAAAuE,QAAA,GAAC,cACf,EAACzG,aAAa,KAAK,SAAS,GAAI,SAAQ8C,KAAK,CAACwF,eAAgB,EAAC,GAAI,QAAOxF,KAAK,CAACwF,eAAgB,EAAC;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzG,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAjEE7C,KAAK;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkEV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENzH,OAAA;QAAK8C,SAAS,EAAC,aAAa;QAAAuE,QAAA,gBAC1BrH,OAAA,CAAC5B,eAAe;UAAC0E,SAAS,EAAC;QAAY;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CzH,OAAA;UAAAqH,QAAA,EAAI;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBzH,OAAA;UAAAqH,QAAA,EAAG;QAA0D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACjEzH,OAAA;UAAG8C,SAAS,EAAC,YAAY;UAAAuE,QAAA,EAAC;QAA2C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLhG,gBAAgB,CAAC4G,MAAM,GAAG,CAAC,IAAI9G,iBAAiB,KAAK,IAAI,iBACxDvB,OAAA;MAAK8C,SAAS,EAAG,iBAAgBnB,eAAe,GAAG,UAAU,GAAG,EAAG,EAAE;MAACyG,OAAO,EAAGL,CAAC,IAAK;QACpF,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACoB,aAAa,EAAEjE,eAAe,CAAC,CAAC;MACrD,CAAE;MAAAmC,QAAA,eACArH,OAAA;QAAK8C,SAAS,EAAG,eAAcnB,eAAe,GAAG,UAAU,GAAG,EAAG,EAAE;QAAA0F,QAAA,EAChE,CAAC,MAAM;UACN,MAAM3D,KAAK,GAAGH,uBAAuB,CAAChC,iBAAiB,CAAC;UACxD,IAAI,CAACmC,KAAK,EAAE,oBAAO1D,OAAA;YAAAqH,QAAA,EAAK;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;UAE7C,oBACEzH,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAAuE,QAAA,gBAC5BrH,OAAA;cAAK8C,SAAS,EAAC,cAAc;cAAAuE,QAAA,gBAC3BrH,OAAA;gBAAK8C,SAAS,EAAC,YAAY;gBAAAuE,QAAA,gBACzBrH,OAAA;kBAAI8C,SAAS,EAAC,aAAa;kBAAAuE,QAAA,EAAE3D,KAAK,CAACO;gBAAK;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9CzH,OAAA;kBAAK8C,SAAS,EAAC,YAAY;kBAAAuE,QAAA,gBACzBrH,OAAA;oBAAM8C,SAAS,EAAC,eAAe;oBAAAuE,QAAA,EAAE3D,KAAK,CAACX;kBAAO;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtDzH,OAAA;oBAAM8C,SAAS,EAAC,aAAa;oBAAAuE,QAAA,GAAC,QAAM,EAAC3D,KAAK,CAACZ,SAAS;kBAAA;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC3D/D,KAAK,CAAC5C,KAAK,iBAAId,OAAA;oBAAM8C,SAAS,EAAC,aAAa;oBAAAuE,QAAA,EAAE3D,KAAK,CAAC5C;kBAAK;oBAAAwG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzH,OAAA;gBAAK8C,SAAS,EAAC,gBAAgB;gBAAAuE,QAAA,gBAC7BrH,OAAA;kBACE8C,SAAS,EAAC,wBAAwB;kBAClCsF,OAAO,EAAEhD,oBAAqB;kBAC9BnB,KAAK,EAAEtC,eAAe,GAAG,iBAAiB,GAAG,kBAAmB;kBAAA0F,QAAA,EAE/D1F,eAAe,gBAAG3B,OAAA,CAACrB,UAAU;oBAAA2I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGzH,OAAA,CAACtB,QAAQ;oBAAA4I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eACTzH,OAAA;kBACE8C,SAAS,EAAC,uBAAuB;kBACjCsF,OAAO,EAAElD,eAAgB;kBACzBjB,KAAK,EAAC,aAAa;kBAAAoD,QAAA,eAEnBrH,OAAA,CAACzB,OAAO;oBAAA+I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNzH,OAAA;cAAK8C,SAAS,EAAC,iBAAiB;cAAAuE,QAAA,EAC7B3D,KAAK,CAACmB,QAAQ,gBACb7E,OAAA;gBAAKoJ,KAAK,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,MAAM;kBAAEC,YAAY,EAAE;gBAAM,CAAE;gBAAAlC,QAAA,gBACrErH,OAAA;kBACEwJ,GAAG,EAAGA,GAAG,IAAKxH,WAAW,CAACwH,GAAG,CAAE;kBAC/BC,QAAQ;kBACRC,QAAQ;kBACRC,WAAW;kBACXC,OAAO,EAAC,UAAU;kBAClBC,KAAK,EAAC,MAAM;kBACZC,MAAM,EAAC,KAAK;kBACZC,MAAM,EAAEjE,eAAe,CAACpC,KAAK,CAAE;kBAC/B0F,KAAK,EAAE;oBACLS,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,OAAO;oBACfE,eAAe,EAAE;kBACnB,CAAE;kBACFxB,OAAO,EAAGT,CAAC,IAAK;oBACdjG,aAAa,CAAE,yBAAwB4B,KAAK,CAACO,KAAM,mCAAkC,CAAC;kBACxF,CAAE;kBACFgG,SAAS,EAAEA,CAAA,KAAM;oBACfnI,aAAa,CAAC,IAAI,CAAC;kBACrB,CAAE;kBACFoI,WAAW,EAAEA,CAAA,KAAM;oBACjB5G,OAAO,CAACuC,GAAG,CAAC,0BAA0B,CAAC;kBACzC,CAAE;kBACFsE,WAAW,EAAC,WAAW;kBAAA9C,QAAA,gBAGvBrH,OAAA;oBAAQsI,GAAG,EAAE5E,KAAK,CAACsB,cAAc,IAAItB,KAAK,CAACmB,QAAS;oBAACqD,IAAI,EAAC;kBAAW;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAGvE/D,KAAK,CAACuF,SAAS,IAAIvF,KAAK,CAACuF,SAAS,CAACZ,MAAM,GAAG,CAAC,IAAI3E,KAAK,CAACuF,SAAS,CAAC7B,GAAG,CAAC,CAACgD,QAAQ,EAAExF,KAAK,kBACpF5E,OAAA;oBAEEqK,IAAI,EAAC,WAAW;oBAChB/B,GAAG,EAAE8B,QAAQ,CAACvB,GAAI;oBAClByB,OAAO,EAAEF,QAAQ,CAACG,QAAS;oBAC3BC,KAAK,EAAEJ,QAAQ,CAACK,YAAa;oBAC7BC,OAAO,EAAEN,QAAQ,CAACO,SAAS,IAAI/F,KAAK,KAAK;kBAAE,GALrC,GAAEwF,QAAQ,CAACG,QAAS,IAAG3F,KAAM,EAAC;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAMrC,CACF,CAAC,EAAC,8CAGL;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAGP/D,KAAK,CAACuF,SAAS,IAAIvF,KAAK,CAACuF,SAAS,CAACZ,MAAM,GAAG,CAAC,iBAC5CrI,OAAA;kBAAK8C,SAAS,EAAC,oBAAoB;kBAAAuE,QAAA,gBACjCrH,OAAA,CAACN,YAAY;oBAACoD,SAAS,EAAC;kBAAe;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1CzH,OAAA;oBAAAqH,QAAA,GAAM,yBAAuB,EAAC3D,KAAK,CAACuF,SAAS,CAACZ,MAAM,EAAC,cAAY;kBAAA;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CACN,EAGA5F,UAAU,iBACT7B,OAAA;kBAAK8C,SAAS,EAAC,qBAAqB;kBAAAuE,QAAA,eAClCrH,OAAA;oBAAK8C,SAAS,EAAC,eAAe;oBAAAuE,QAAA,gBAC5BrH,OAAA,CAACP,eAAe;sBAACqD,SAAS,EAAC;oBAAY;sBAAAwE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1CzH,OAAA;sBAAAqH,QAAA,EAAIxF;oBAAU;sBAAAyF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnBzH,OAAA;sBAAQoI,OAAO,EAAEA,CAAA,KAAMtG,aAAa,CAAC,IAAI,CAAE;sBAACgB,SAAS,EAAC,mBAAmB;sBAAAuE,QAAA,EAAC;oBAE1E;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,GACN/D,KAAK,CAACsC,OAAO;cAAA;cACf;cACAhG,OAAA;gBACEsI,GAAG,EAAG,iCAAgC5E,KAAK,CAACsC,OAAQ,mBAAmB;gBACvE/B,KAAK,EAAEP,KAAK,CAACO,KAAM;gBACnB2G,WAAW,EAAC,GAAG;gBACfC,eAAe;gBACf/H,SAAS,EAAC,cAAc;gBACxBgI,MAAM,EAAEA,CAAA,KAAMxH,OAAO,CAACuC,GAAG,CAAC,yBAAyB;cAAE;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,gBAEVzH,OAAA;gBAAK8C,SAAS,EAAC,aAAa;gBAAAuE,QAAA,gBAC1BrH,OAAA;kBAAK8C,SAAS,EAAC,YAAY;kBAAAuE,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpCzH,OAAA;kBAAAqH,QAAA,EAAI;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1BzH,OAAA;kBAAAqH,QAAA,EAAIxF,UAAU,IAAI;gBAA4C;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnEzH,OAAA;kBAAK8C,SAAS,EAAC,eAAe;kBAAAuE,QAAA,eAC5BrH,OAAA;oBACE+K,IAAI,EAAErH,KAAK,CAACsB,cAAc,IAAItB,KAAK,CAACmB,QAAS;oBAC7CmD,MAAM,EAAC,QAAQ;oBACfgD,GAAG,EAAC,qBAAqB;oBACzBlI,SAAS,EAAC,mBAAmB;oBAAAuE,QAAA,EAC9B;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGL,CAAC9F,eAAe,iBACf3B,OAAA;cAAK8C,SAAS,EAAC,kBAAkB;cAAAuE,QAAA,gBAC/BrH,OAAA;gBAAI8C,SAAS,EAAC,gBAAgB;gBAAAuE,QAAA,gBAC5BrH,OAAA,CAACN,YAAY;kBAAA4H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBACJ,EAACxF,QAAQ,CAACoG,MAAM,EAAC,GAC/B;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAGLzH,OAAA;gBAAK8C,SAAS,EAAC,aAAa;gBAAAuE,QAAA,eAC1BrH,OAAA;kBAAK8C,SAAS,EAAC,yBAAyB;kBAAAuE,QAAA,gBACtCrH,OAAA;oBACE6H,KAAK,EAAE1F,UAAW;oBAClB2F,QAAQ,EAAGC,CAAC,IAAK3F,aAAa,CAAC2F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAC/CM,WAAW,EAAC,yCAAyC;oBACrDrF,SAAS,EAAC,eAAe;oBACzBmI,IAAI,EAAC;kBAAG;oBAAA3D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACFzH,OAAA;oBACEoI,OAAO,EAAE7B,gBAAiB;oBAC1BzD,SAAS,EAAC,oBAAoB;oBAC9BoI,QAAQ,EAAE,CAAC/I,UAAU,CAACwB,IAAI,CAAC,CAAE;oBAAA0D,QAAA,EAC9B;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNzH,OAAA;gBAAK8C,SAAS,EAAC,eAAe;gBAAAuE,QAAA,EAC3BpF,QAAQ,CAACoG,MAAM,KAAK,CAAC,gBACpBrI,OAAA;kBAAK8C,SAAS,EAAC,aAAa;kBAAAuE,QAAA,gBAC1BrH,OAAA,CAACN,YAAY;oBAAA4H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChBzH,OAAA;oBAAAqH,QAAA,EAAG;kBAAqD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC,GAENxF,QAAQ,CAACmF,GAAG,CAAEZ,OAAO,iBACnBxG,OAAA;kBAAsB8C,SAAS,EAAC,SAAS;kBAAAuE,QAAA,gBACvCrH,OAAA;oBAAK8C,SAAS,EAAC,gBAAgB;oBAAAuE,QAAA,gBAC7BrH,OAAA;sBAAM8C,SAAS,EAAC,gBAAgB;sBAAAuE,QAAA,EAAEb,OAAO,CAACI;oBAAM;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACxDzH,OAAA;sBAAM8C,SAAS,EAAC,cAAc;sBAAAuE,QAAA,EAC3B,IAAI7C,IAAI,CAACgC,OAAO,CAACM,SAAS,CAAC,CAACqE,kBAAkB,CAAC;oBAAC;sBAAA7D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNzH,OAAA;oBAAK8C,SAAS,EAAC,cAAc;oBAAAuE,QAAA,EAAEb,OAAO,CAACG;kBAAI;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClDzH,OAAA;oBAAK8C,SAAS,EAAC,iBAAiB;oBAAAuE,QAAA,eAC9BrH,OAAA;sBACEoI,OAAO,EAAEA,CAAA,KAAM9F,aAAa,CAACkE,OAAO,CAACC,EAAE,CAAE;sBACzC3D,SAAS,EAAC,WAAW;sBAAAuE,QAAA,EACtB;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,EAGLpF,UAAU,KAAKmE,OAAO,CAACC,EAAE,iBACxBzG,OAAA;oBAAK8C,SAAS,EAAC,uBAAuB;oBAAAuE,QAAA,gBACpCrH,OAAA;sBACE6H,KAAK,EAAEtF,SAAU;sBACjBuF,QAAQ,EAAGC,CAAC,IAAKvF,YAAY,CAACuF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;sBAC9CM,WAAW,EAAC,kBAAkB;sBAC9BrF,SAAS,EAAC,aAAa;sBACvBmI,IAAI,EAAC;oBAAG;sBAAA3D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC,eACFzH,OAAA;sBAAK8C,SAAS,EAAC,eAAe;sBAAAuE,QAAA,gBAC5BrH,OAAA;wBACEoI,OAAO,EAAEA,CAAA,KAAMnB,cAAc,CAACT,OAAO,CAACC,EAAE,CAAE;wBAC1C3D,SAAS,EAAC,kBAAkB;wBAC5BoI,QAAQ,EAAE,CAAC3I,SAAS,CAACoB,IAAI,CAAC,CAAE;wBAAA0D,QAAA,EAC7B;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACTzH,OAAA;wBACEoI,OAAO,EAAEA,CAAA,KAAM;0BACb9F,aAAa,CAAC,IAAI,CAAC;0BACnBE,YAAY,CAAC,EAAE,CAAC;wBAClB,CAAE;wBACFM,SAAS,EAAC,kBAAkB;wBAAAuE,QAAA,EAC7B;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,EAGAjB,OAAO,CAACQ,OAAO,CAACqB,MAAM,GAAG,CAAC,iBACzBrI,OAAA;oBAAK8C,SAAS,EAAC,SAAS;oBAAAuE,QAAA,EACrBb,OAAO,CAACQ,OAAO,CAACI,GAAG,CAAED,KAAK,iBACzBnH,OAAA;sBAAoB8C,SAAS,EAAC,OAAO;sBAAAuE,QAAA,gBACnCrH,OAAA;wBAAK8C,SAAS,EAAC,cAAc;wBAAAuE,QAAA,gBAC3BrH,OAAA;0BAAM8C,SAAS,EAAC,cAAc;0BAAAuE,QAAA,EAAEF,KAAK,CAACP;wBAAM;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eACpDzH,OAAA;0BAAM8C,SAAS,EAAC,YAAY;0BAAAuE,QAAA,EACzB,IAAI7C,IAAI,CAAC2C,KAAK,CAACL,SAAS,CAAC,CAACqE,kBAAkB,CAAC;wBAAC;0BAAA7D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3C,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNzH,OAAA;wBAAK8C,SAAS,EAAC,YAAY;wBAAAuE,QAAA,EAAEF,KAAK,CAACR;sBAAI;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA,GAPtCN,KAAK,CAACV,EAAE;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAQb,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CACN;gBAAA,GA/DOjB,OAAO,CAACC,EAAE;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgEf,CACN;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAEV,CAAC,EAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACvH,EAAA,CAzvBQD,YAAY;EAAA,QACFlC,WAAW,EACXD,WAAW;AAAA;AAAAsN,EAAA,GAFrBnL,YAAY;AA2vBrB,eAAeA,YAAY;AAAC,IAAAmL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}