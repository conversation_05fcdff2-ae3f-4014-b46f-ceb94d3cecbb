{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { FaPlayCircle, FaVideo, FaGraduationCap, FaDownload, FaEye, FaTimes, FaChevronDown, FaSearch, FaExpand, FaCompress } from \"react-icons/fa\";\nimport { TbVideo, TbSchool, <PERSON>b<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Tb<PERSON>ortAscending, Tb<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON><PERSON>, TbChevronDown as TbChevronDownIcon, TbChevronUp, TbX, TbAlertTriangle, TbInfoCircle, TbCheck } from \"react-icons/tb\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction VideoLessons() {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const dispatch = useDispatch();\n\n  // State management\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedLevel, setSelectedLevel] = useState((user === null || user === void 0 ? void 0 : user.level) || \"primary\");\n  const [selectedClass, setSelectedClass] = useState((user === null || user === void 0 ? void 0 : user.class) || \"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Video player state\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [videoError, setVideoError] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n\n  // Comments state\n  const [comments, setComments] = useState([]);\n  const [newComment, setNewComment] = useState(\"\");\n  const [replyingTo, setReplyingTo] = useState(null);\n  const [replyText, setReplyText] = useState(\"\");\n\n  // Available classes based on level\n  const availableClasses = useMemo(() => {\n    if (selectedLevel === \"primary\") return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    if (selectedLevel === \"secondary\") return [\"1\", \"2\", \"3\", \"4\"];\n    if (selectedLevel === \"advance\") return [\"5\", \"6\"];\n    return [];\n  }, [selectedLevel]);\n\n  // Available subjects based on level\n  const availableSubjects = useMemo(() => {\n    if (selectedLevel === \"primary\") return primarySubjects;\n    if (selectedLevel === \"secondary\") return secondarySubjects;\n    if (selectedLevel === \"advance\") return advanceSubjects;\n    return [];\n  }, [selectedLevel]);\n\n  // Fetch videos\n  const fetchVideos = useCallback(async () => {\n    try {\n      var _response$data;\n      setLoading(true);\n      setError(null);\n      dispatch(ShowLoading());\n      const filters = {\n        level: selectedLevel,\n        className: \"\",\n        // Get all classes for the level\n        subject: \"\",\n        // Get all subjects for the level\n        content: \"videos\"\n      };\n      const response = await getStudyMaterial(filters);\n      if (response !== null && response !== void 0 && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.success) {\n        setVideos(response.data.data || []);\n      } else {\n        var _response$data2;\n        setError((response === null || response === void 0 ? void 0 : (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message) || \"Failed to fetch videos\");\n        setVideos([]);\n      }\n    } catch (error) {\n      console.error(\"Error fetching videos:\", error);\n      setError(\"Failed to load videos. Please try again.\");\n      setVideos([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [selectedLevel, dispatch]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    console.log('🔍 Starting filter with:', {\n      totalVideos: videos.length,\n      selectedLevel,\n      selectedClass,\n      selectedSubject,\n      searchTerm\n    });\n    let filtered = videos;\n\n    // Apply level filter\n    const beforeLevel = filtered.length;\n    filtered = filtered.filter(video => {\n      const matches = video.level === selectedLevel;\n      if (!matches) {\n        console.log('❌ Level filter rejected:', video.title, 'level:', video.level, 'expected:', selectedLevel);\n      }\n      return matches;\n    });\n    console.log(`📊 Level filter: ${beforeLevel} → ${filtered.length}`);\n\n    // Apply class filter\n    if (selectedClass !== \"all\") {\n      const beforeClass = filtered.length;\n      filtered = filtered.filter(video => {\n        // Check both className and class fields for compatibility\n        const videoClass = video.className || video.class;\n        const matches = videoClass === selectedClass;\n        if (!matches) {\n          console.log('❌ Class filter rejected:', video.title, 'class:', videoClass, 'expected:', selectedClass);\n        }\n        return matches;\n      });\n      console.log(`📊 Class filter: ${beforeClass} → ${filtered.length}`);\n    }\n\n    // Apply subject filter\n    if (selectedSubject !== \"all\") {\n      const beforeSubject = filtered.length;\n      filtered = filtered.filter(video => video.subject === selectedSubject);\n      console.log(`📊 Subject filter: ${beforeSubject} → ${filtered.length}`);\n    }\n\n    // Apply search filter\n    if (searchTerm.trim()) {\n      const beforeSearch = filtered.length;\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(video => {\n        var _video$title, _video$subject, _video$topic;\n        return ((_video$title = video.title) === null || _video$title === void 0 ? void 0 : _video$title.toLowerCase().includes(searchLower)) || ((_video$subject = video.subject) === null || _video$subject === void 0 ? void 0 : _video$subject.toLowerCase().includes(searchLower)) || ((_video$topic = video.topic) === null || _video$topic === void 0 ? void 0 : _video$topic.toLowerCase().includes(searchLower));\n      });\n      console.log(`📊 Search filter: ${beforeSearch} → ${filtered.length}`);\n    }\n\n    // Apply sorting\n    const sorted = [...filtered].sort((a, b) => {\n      switch (sortBy) {\n        case \"newest\":\n          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);\n        case \"oldest\":\n          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);\n        case \"title\":\n          return (a.title || \"\").localeCompare(b.title || \"\");\n        case \"subject\":\n          return (a.subject || \"\").localeCompare(b.subject || \"\");\n        default:\n          return 0;\n      }\n    });\n    console.log('✅ Final result:', sorted.length, 'videos');\n    return sorted;\n  }, [videos, searchTerm, sortBy, selectedLevel, selectedClass, selectedSubject]);\n\n  // Video handlers\n  const handleShowVideo = async index => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video !== null && video !== void 0 && video.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    if (videoRef) {\n      videoRef.pause();\n    }\n  };\n  const toggleVideoExpansion = () => {\n    setIsVideoExpanded(!isVideoExpanded);\n  };\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async videoUrl => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.error('❌ Error getting signed URL:', error);\n        return videoUrl;\n      }\n    }\n    return videoUrl;\n  };\n\n  // Get thumbnail URL\n  const getThumbnailUrl = video => {\n    if (video.thumbnail) {\n      return video.thumbnail;\n    }\n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      let videoId = video.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    return '/api/placeholder/400/225';\n  };\n\n  // Effects\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.level) {\n      setSelectedLevel(user.level);\n    }\n    if (user !== null && user !== void 0 && user.class) {\n      setSelectedClass(user.class);\n    }\n  }, [user]);\n\n  // Clear search and refresh\n  const handleClearSearch = () => {\n    setSearchTerm(\"\");\n  };\n  const handleRefresh = () => {\n    // Only refresh data, don't clear filters or search\n    fetchVideos();\n  };\n  const handleClearAll = () => {\n    setSearchTerm(\"\");\n    setSelectedSubject(\"all\");\n    setSelectedClass(\"all\");\n    fetchVideos();\n  };\n\n  // Comment functions\n  const handleAddComment = () => {\n    if (newComment.trim()) {\n      const comment = {\n        id: Date.now(),\n        text: newComment,\n        author: (user === null || user === void 0 ? void 0 : user.name) || \"Anonymous\",\n        timestamp: new Date().toISOString(),\n        replies: []\n      };\n      setComments([...comments, comment]);\n      setNewComment(\"\");\n    }\n  };\n  const handleAddReply = commentId => {\n    if (replyText.trim()) {\n      const reply = {\n        id: Date.now(),\n        text: replyText,\n        author: (user === null || user === void 0 ? void 0 : user.name) || \"Anonymous\",\n        timestamp: new Date().toISOString()\n      };\n      setComments(comments.map(comment => comment.id === commentId ? {\n        ...comment,\n        replies: [...comment.replies, reply]\n      } : comment));\n      setReplyText(\"\");\n      setReplyingTo(null);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-lessons-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-main\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-icon\",\n            children: /*#__PURE__*/_jsxDEV(TbVideo, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              children: \"Video Lessons\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Watch educational videos to enhance your learning\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"level-display\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"current-level\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"level-label\",\n              children: \"Level:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"level-value\",\n              children: selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"current-class\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"class-label\",\n              children: \"Your Class:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"class-value\",\n              children: (user === null || user === void 0 ? void 0 : user.level) === 'primary' ? `Class ${(user === null || user === void 0 ? void 0 : user.class) || 'N/A'}` : (user === null || user === void 0 ? void 0 : user.level) === 'secondary' ? `Form ${(user === null || user === void 0 ? void 0 : user.class) || 'N/A'}` : (user === null || user === void 0 ? void 0 : user.level) === 'advance' ? `Form ${(user === null || user === void 0 ? void 0 : user.class) || 'N/A'}` : 'Not Set'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"controls-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbSchool, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 17\n              }, this), \"Level\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedLevel,\n              onChange: e => setSelectedLevel(e.target.value),\n              className: \"control-select level-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"primary\",\n                children: \"Primary (1-7)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"secondary\",\n                children: \"Secondary (1-4)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"advance\",\n                children: \"Advance (5-6)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbFilter, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 17\n              }, this), \"Filter by Class\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedClass,\n              onChange: e => setSelectedClass(e.target.value),\n              className: \"control-select class-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Classes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 17\n              }, this), availableClasses.map(cls => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: cls,\n                children: selectedLevel === 'primary' ? `Class ${cls}` : `Form ${cls}`\n              }, cls, false, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbFilter, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this), \"Subject\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedSubject,\n              onChange: e => setSelectedSubject(e.target.value),\n              className: \"control-select subject-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Subjects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 17\n              }, this), availableSubjects.map(subject => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: subject,\n                children: subject\n              }, subject, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbSortAscending, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 17\n              }, this), \"Sort\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: sortBy,\n              onChange: e => setSortBy(e.target.value),\n              className: \"control-select sort-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"newest\",\n                children: \"Newest First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"oldest\",\n                children: \"Oldest First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"title\",\n                children: \"Title A-Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"subject\",\n                children: \"Subject A-Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-container\",\n            children: [/*#__PURE__*/_jsxDEV(TbSearch, {\n              className: \"search-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search videos by title, subject, or topic...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              className: \"search-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 15\n            }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleClearSearch,\n              className: \"clear-search-btn\",\n              children: [/*#__PURE__*/_jsxDEV(TbX, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 19\n              }, this), \"Clear Search\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRefresh,\n            className: \"refresh-btn\",\n            children: [/*#__PURE__*/_jsxDEV(TbDownload, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this), \"Refresh All\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading videos...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-state\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n          className: \"error-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Error Loading Videos\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchVideos,\n          className: \"retry-btn\",\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 11\n      }, this) : filteredAndSortedVideos.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"videos-grid\",\n        children: filteredAndSortedVideos.map((video, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"video-card\",\n          onClick: () => handleShowVideo(index),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-card-thumbnail\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: getThumbnailUrl(video),\n              alt: video.title,\n              className: \"thumbnail-image\",\n              onError: e => {\n                // Fallback logic for failed thumbnails\n                if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                  // For YouTube videos, try different quality thumbnails\n                  let videoId = video.videoID;\n                  if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                    const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                    videoId = match ? match[1] : videoId;\n                  }\n                  const fallbacks = [`https://img.youtube.com/vi/${videoId}/hqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/default.jpg`, '/api/placeholder/320/180'];\n                  const currentSrc = e.target.src;\n                  const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                  if (currentIndex < fallbacks.length - 1) {\n                    e.target.src = fallbacks[currentIndex + 1];\n                  }\n                } else {\n                  e.target.src = '/api/placeholder/320/180';\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"play-overlay\",\n              children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                className: \"play-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-duration\",\n              children: video.duration || \"Video\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 19\n            }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"subtitle-badge\",\n              children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 23\n              }, this), \"CC\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-card-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"video-title\",\n              children: video.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"video-subject\",\n                children: video.subject\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"video-class\",\n                children: selectedLevel === 'primary' ? `Class ${video.className || video.class}` : `Form ${video.className || video.class}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-tags\",\n              children: [video.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"topic-tag\",\n                children: video.topic\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 37\n              }, this), video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"shared-tag\",\n                children: [\"Shared from \", selectedLevel === 'primary' ? `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 503,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n          className: \"empty-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No Videos Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No video lessons are available for your current selection.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"suggestion\",\n          children: \"Try selecting a different class or subject.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 572,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 7\n    }, this), showVideoIndices.length > 0 && currentVideoIndex !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `video-overlay ${isVideoExpanded ? 'expanded' : ''}`,\n      onClick: e => {\n        if (e.target === e.currentTarget) handleHideVideo();\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `video-modal ${isVideoExpanded ? 'expanded' : ''}`,\n        children: (() => {\n          const video = filteredAndSortedVideos[currentVideoIndex];\n          if (!video) return /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Video not found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 34\n          }, this);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"video-title\",\n                  children: video.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 595,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-subject\",\n                    children: video.subject\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 597,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-class\",\n                    children: [\"Class \", video.className]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 25\n                  }, this), video.level && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-level\",\n                    children: video.level\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 599,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-controls\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"control-btn expand-btn\",\n                  onClick: toggleVideoExpansion,\n                  title: isVideoExpanded ? \"Exit fullscreen\" : \"Enter fullscreen\",\n                  children: isVideoExpanded ? /*#__PURE__*/_jsxDEV(FaCompress, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 608,\n                    columnNumber: 44\n                  }, this) : /*#__PURE__*/_jsxDEV(FaExpand, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 608,\n                    columnNumber: 61\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 603,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"control-btn close-btn\",\n                  onClick: handleHideVideo,\n                  title: \"Close video\",\n                  children: /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 615,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 610,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-container\",\n              children: video.videoUrl ? /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '15px',\n                  background: '#000',\n                  borderRadius: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"video\", {\n                  ref: ref => setVideoRef(ref),\n                  controls: true,\n                  autoPlay: true,\n                  playsInline: true,\n                  preload: \"metadata\",\n                  width: \"100%\",\n                  height: \"400\",\n                  poster: getThumbnailUrl(video),\n                  style: {\n                    width: '100%',\n                    height: '400px',\n                    backgroundColor: '#000'\n                  },\n                  onError: e => {\n                    setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                  },\n                  onCanPlay: () => {\n                    setVideoError(null);\n                  },\n                  onLoadStart: () => {\n                    console.log('🎬 Video loading started');\n                  },\n                  crossOrigin: \"anonymous\",\n                  children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                    src: video.signedVideoUrl || video.videoUrl,\n                    type: \"video/mp4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 648,\n                    columnNumber: 29\n                  }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => /*#__PURE__*/_jsxDEV(\"track\", {\n                    kind: \"subtitles\",\n                    src: subtitle.url,\n                    srcLang: subtitle.language,\n                    label: subtitle.languageName,\n                    default: subtitle.isDefault || index === 0\n                  }, `${subtitle.language}-${index}`, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 652,\n                    columnNumber: 31\n                  }, this)), \"Your browser does not support the video tag.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 622,\n                  columnNumber: 27\n                }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"subtitle-indicator\",\n                  children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {\n                    className: \"subtitle-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 668,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Subtitles available in \", video.subtitles.length, \" language(s)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 669,\n                    columnNumber: 31\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 667,\n                  columnNumber: 29\n                }, this), videoError && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-error-overlay\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"error-content\",\n                    children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n                      className: \"error-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 677,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: videoError\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 678,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setVideoError(null),\n                      className: \"dismiss-error-btn\",\n                      children: \"Dismiss\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 679,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 676,\n                    columnNumber: 31\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 675,\n                  columnNumber: 29\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 23\n              }, this) : video.videoID ?\n              /*#__PURE__*/\n              // Fallback to YouTube embed if no videoUrl\n              _jsxDEV(\"iframe\", {\n                src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                title: video.title,\n                frameBorder: \"0\",\n                allowFullScreen: true,\n                className: \"video-iframe\",\n                onLoad: () => console.log('✅ YouTube iframe loaded')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-error\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"error-icon\",\n                  children: \"\\u26A0\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 698,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"Video Unavailable\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 699,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: videoError || \"This video cannot be played at the moment.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"error-actions\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: video.signedVideoUrl || video.videoUrl,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: \"external-link-btn\",\n                    children: \"\\uD83D\\uDCF1 Open in New Tab\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 702,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 701,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 697,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 19\n            }, this), !isVideoExpanded && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"comments-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"comments-title\",\n                children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 719,\n                  columnNumber: 25\n                }, this), \"Discussion (\", comments.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"add-comment\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"comment-input-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                    value: newComment,\n                    onChange: e => setNewComment(e.target.value),\n                    placeholder: \"Share your thoughts about this video...\",\n                    className: \"comment-input\",\n                    rows: \"3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 726,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: handleAddComment,\n                    className: \"comment-submit-btn\",\n                    disabled: !newComment.trim(),\n                    children: \"Post Comment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 733,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 725,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"comments-list\",\n                children: comments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"no-comments\",\n                  children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 747,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"No comments yet. Be the first to share your thoughts!\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 748,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 746,\n                  columnNumber: 27\n                }, this) : comments.map(comment => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"comment\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"comment-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"comment-author\",\n                      children: comment.author\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 754,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"comment-time\",\n                      children: new Date(comment.timestamp).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 755,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 753,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"comment-text\",\n                    children: comment.text\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 759,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"comment-actions\",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setReplyingTo(comment.id),\n                      className: \"reply-btn\",\n                      children: \"Reply\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 761,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 760,\n                    columnNumber: 31\n                  }, this), replyingTo === comment.id && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"reply-input-container\",\n                    children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                      value: replyText,\n                      onChange: e => setReplyText(e.target.value),\n                      placeholder: \"Write a reply...\",\n                      className: \"reply-input\",\n                      rows: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 772,\n                      columnNumber: 35\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"reply-actions\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleAddReply(comment.id),\n                        className: \"reply-submit-btn\",\n                        disabled: !replyText.trim(),\n                        children: \"Reply\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 780,\n                        columnNumber: 37\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setReplyingTo(null);\n                          setReplyText(\"\");\n                        },\n                        className: \"reply-cancel-btn\",\n                        children: \"Cancel\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 787,\n                        columnNumber: 37\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 779,\n                      columnNumber: 35\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 771,\n                    columnNumber: 33\n                  }, this), comment.replies.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"replies\",\n                    children: comment.replies.map(reply => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"reply\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"reply-header\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"reply-author\",\n                          children: reply.author\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 806,\n                          columnNumber: 41\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"reply-time\",\n                          children: new Date(reply.timestamp).toLocaleDateString()\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 807,\n                          columnNumber: 41\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 805,\n                        columnNumber: 39\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"reply-text\",\n                        children: reply.text\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 811,\n                        columnNumber: 39\n                      }, this)]\n                    }, reply.id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 804,\n                      columnNumber: 37\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 802,\n                    columnNumber: 33\n                  }, this)]\n                }, comment.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 752,\n                  columnNumber: 29\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 17\n          }, this);\n        })()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 586,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 583,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 346,\n    columnNumber: 5\n  }, this);\n}\n_s(VideoLessons, \"I4mp5eaYZbyYl/F2gkauBdV46uI=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = VideoLessons;\nexport default VideoLessons;\nvar _c;\n$RefreshReg$(_c, \"VideoLessons\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "motion", "AnimatePresence", "getStudyMaterial", "useDispatch", "useSelector", "HideLoading", "ShowLoading", "FaPlayCircle", "FaVideo", "FaGraduationCap", "FaDownload", "FaEye", "FaTimes", "FaChevronDown", "FaSearch", "FaExpand", "FaCompress", "TbVideo", "TbSchool", "TbSearch", "Tb<PERSON><PERSON>er", "TbSortAscending", "TbDownload", "TbEye", "TbCalendar", "TbUser", "TbChevronDown", "TbChevronDownIcon", "TbChevronUp", "TbX", "TbAlertTriangle", "TbInfoCircle", "TbCheck", "primarySubjects", "secondarySubjects", "advanceSubjects", "jsxDEV", "_jsxDEV", "VideoLessons", "_s", "user", "state", "dispatch", "videos", "setVideos", "loading", "setLoading", "error", "setError", "selectedLevel", "setSelectedLevel", "level", "selectedClass", "setSelectedClass", "class", "selectedSubject", "setSelectedSubject", "searchTerm", "setSearchTerm", "sortBy", "setSortBy", "currentVideoIndex", "setCurrentVideoIndex", "showVideoIndices", "setShowVideoIndices", "isVideoExpanded", "setIsVideoExpanded", "videoError", "setVideoError", "videoRef", "setVideoRef", "comments", "setComments", "newComment", "setNewComment", "replyingTo", "setReplyingTo", "replyText", "setReplyText", "availableClasses", "availableSubjects", "fetchVideos", "_response$data", "filters", "className", "subject", "content", "response", "data", "success", "_response$data2", "message", "console", "filteredAndSortedVideos", "log", "totalVideos", "length", "filtered", "beforeLevel", "filter", "video", "matches", "title", "beforeClass", "videoClass", "beforeSubject", "trim", "beforeSearch", "searchLower", "toLowerCase", "_video$title", "_video$subject", "_video$topic", "includes", "topic", "sorted", "sort", "a", "b", "Date", "createdAt", "localeCompare", "handleShowVideo", "index", "videoUrl", "signedUrl", "getSignedVideoUrl", "signedVideoUrl", "warn", "handleHideVideo", "pause", "toggleVideoExpansion", "fetch", "encodeURIComponent", "method", "headers", "ok", "Error", "status", "json", "getThumbnailUrl", "thumbnail", "videoID", "videoId", "match", "handleClearSearch", "handleRefresh", "handleClearAll", "handleAddComment", "comment", "id", "now", "text", "author", "name", "timestamp", "toISOString", "replies", "handleAddReply", "commentId", "reply", "map", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "char<PERSON>t", "toUpperCase", "slice", "value", "onChange", "e", "target", "cls", "type", "placeholder", "onClick", "src", "alt", "onError", "fallbacks", "currentSrc", "currentIndex", "findIndex", "url", "split", "pop", "duration", "subtitles", "sharedFromClass", "currentTarget", "style", "padding", "background", "borderRadius", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "poster", "backgroundColor", "onCanPlay", "onLoadStart", "crossOrigin", "subtitle", "kind", "srcLang", "language", "label", "languageName", "default", "isDefault", "frameBorder", "allowFullScreen", "onLoad", "href", "rel", "rows", "disabled", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/index.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\n\nimport {\n  FaPlayCircle,\n  FaVideo,\n  FaGraduationCap,\n  FaDownload,\n  FaEye,\n  FaTimes,\n  FaChevronDown,\n  FaSearch,\n  FaExpand,\n  FaCompress,\n} from \"react-icons/fa\";\nimport {\n  TbVideo,\n  TbSchool,\n  TbSearch,\n  TbFilter,\n  TbSortAscending,\n  TbDownload,\n  TbEye,\n  TbCalendar,\n  TbUser,\n  TbChevronDown as TbChevronDownIcon,\n  TbChevronUp,\n  TbX,\n  Tb<PERSON>lert<PERSON>riangle,\n  TbIn<PERSON><PERSON><PERSON><PERSON>,\n  Tb<PERSON><PERSON><PERSON>,\n} from \"react-icons/tb\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\n\nfunction VideoLessons() {\n  const { user } = useSelector((state) => state.user);\n  const dispatch = useDispatch();\n\n  // State management\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedLevel, setSelectedLevel] = useState(user?.level || \"primary\");\n  const [selectedClass, setSelectedClass] = useState(user?.class || \"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Video player state\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [videoError, setVideoError] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n\n  // Comments state\n  const [comments, setComments] = useState([]);\n  const [newComment, setNewComment] = useState(\"\");\n  const [replyingTo, setReplyingTo] = useState(null);\n  const [replyText, setReplyText] = useState(\"\");\n\n  // Available classes based on level\n  const availableClasses = useMemo(() => {\n    if (selectedLevel === \"primary\") return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    if (selectedLevel === \"secondary\") return [\"1\", \"2\", \"3\", \"4\"];\n    if (selectedLevel === \"advance\") return [\"5\", \"6\"];\n    return [];\n  }, [selectedLevel]);\n\n  // Available subjects based on level\n  const availableSubjects = useMemo(() => {\n    if (selectedLevel === \"primary\") return primarySubjects;\n    if (selectedLevel === \"secondary\") return secondarySubjects;\n    if (selectedLevel === \"advance\") return advanceSubjects;\n    return [];\n  }, [selectedLevel]);\n\n  // Fetch videos\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      dispatch(ShowLoading());\n\n      const filters = {\n        level: selectedLevel,\n        className: \"\", // Get all classes for the level\n        subject: \"\", // Get all subjects for the level\n        content: \"videos\"\n      };\n\n      const response = await getStudyMaterial(filters);\n\n      if (response?.data?.success) {\n        setVideos(response.data.data || []);\n      } else {\n        setError(response?.data?.message || \"Failed to fetch videos\");\n        setVideos([]);\n      }\n    } catch (error) {\n      console.error(\"Error fetching videos:\", error);\n      setError(\"Failed to load videos. Please try again.\");\n      setVideos([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [selectedLevel, dispatch]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    console.log('🔍 Starting filter with:', {\n      totalVideos: videos.length,\n      selectedLevel,\n      selectedClass,\n      selectedSubject,\n      searchTerm\n    });\n\n    let filtered = videos;\n\n    // Apply level filter\n    const beforeLevel = filtered.length;\n    filtered = filtered.filter(video => {\n      const matches = video.level === selectedLevel;\n      if (!matches) {\n        console.log('❌ Level filter rejected:', video.title, 'level:', video.level, 'expected:', selectedLevel);\n      }\n      return matches;\n    });\n    console.log(`📊 Level filter: ${beforeLevel} → ${filtered.length}`);\n\n    // Apply class filter\n    if (selectedClass !== \"all\") {\n      const beforeClass = filtered.length;\n      filtered = filtered.filter(video => {\n        // Check both className and class fields for compatibility\n        const videoClass = video.className || video.class;\n        const matches = videoClass === selectedClass;\n        if (!matches) {\n          console.log('❌ Class filter rejected:', video.title, 'class:', videoClass, 'expected:', selectedClass);\n        }\n        return matches;\n      });\n      console.log(`📊 Class filter: ${beforeClass} → ${filtered.length}`);\n    }\n\n    // Apply subject filter\n    if (selectedSubject !== \"all\") {\n      const beforeSubject = filtered.length;\n      filtered = filtered.filter(video => video.subject === selectedSubject);\n      console.log(`📊 Subject filter: ${beforeSubject} → ${filtered.length}`);\n    }\n\n    // Apply search filter\n    if (searchTerm.trim()) {\n      const beforeSearch = filtered.length;\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(video =>\n        video.title?.toLowerCase().includes(searchLower) ||\n        video.subject?.toLowerCase().includes(searchLower) ||\n        video.topic?.toLowerCase().includes(searchLower)\n      );\n      console.log(`📊 Search filter: ${beforeSearch} → ${filtered.length}`);\n    }\n\n    // Apply sorting\n    const sorted = [...filtered].sort((a, b) => {\n      switch (sortBy) {\n        case \"newest\":\n          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);\n        case \"oldest\":\n          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);\n        case \"title\":\n          return (a.title || \"\").localeCompare(b.title || \"\");\n        case \"subject\":\n          return (a.subject || \"\").localeCompare(b.subject || \"\");\n        default:\n          return 0;\n      }\n    });\n\n    console.log('✅ Final result:', sorted.length, 'videos');\n    return sorted;\n  }, [videos, searchTerm, sortBy, selectedLevel, selectedClass, selectedSubject]);\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedVideos[index];\n\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    if (videoRef) {\n      videoRef.pause();\n    }\n  };\n\n  const toggleVideoExpansion = () => {\n    setIsVideoExpanded(!isVideoExpanded);\n  };\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async (videoUrl) => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n          }\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.error('❌ Error getting signed URL:', error);\n        return videoUrl;\n      }\n    }\n\n    return videoUrl;\n  };\n\n  // Get thumbnail URL\n  const getThumbnailUrl = (video) => {\n    if (video.thumbnail) {\n      return video.thumbnail;\n    }\n    \n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      let videoId = video.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    \n    return '/api/placeholder/400/225';\n  };\n\n  // Effects\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  useEffect(() => {\n    if (user?.level) {\n      setSelectedLevel(user.level);\n    }\n    if (user?.class) {\n      setSelectedClass(user.class);\n    }\n  }, [user]);\n\n  // Clear search and refresh\n  const handleClearSearch = () => {\n    setSearchTerm(\"\");\n  };\n\n  const handleRefresh = () => {\n    // Only refresh data, don't clear filters or search\n    fetchVideos();\n  };\n\n  const handleClearAll = () => {\n    setSearchTerm(\"\");\n    setSelectedSubject(\"all\");\n    setSelectedClass(\"all\");\n    fetchVideos();\n  };\n\n  // Comment functions\n  const handleAddComment = () => {\n    if (newComment.trim()) {\n      const comment = {\n        id: Date.now(),\n        text: newComment,\n        author: user?.name || \"Anonymous\",\n        timestamp: new Date().toISOString(),\n        replies: []\n      };\n      setComments([...comments, comment]);\n      setNewComment(\"\");\n    }\n  };\n\n  const handleAddReply = (commentId) => {\n    if (replyText.trim()) {\n      const reply = {\n        id: Date.now(),\n        text: replyText,\n        author: user?.name || \"Anonymous\",\n        timestamp: new Date().toISOString()\n      };\n\n      setComments(comments.map(comment =>\n        comment.id === commentId\n          ? { ...comment, replies: [...comment.replies, reply] }\n          : comment\n      ));\n      setReplyText(\"\");\n      setReplyingTo(null);\n    }\n  };\n\n  return (\n    <div className=\"video-lessons-container\">\n      {/* Enhanced Header with Level Display */}\n      <div className=\"video-lessons-header\">\n        <div className=\"header-content\">\n          <div className=\"header-main\">\n            <div className=\"header-icon\">\n              <TbVideo />\n            </div>\n            <div className=\"header-text\">\n              <h1>Video Lessons</h1>\n              <p>Watch educational videos to enhance your learning</p>\n            </div>\n          </div>\n\n          {/* Level and Class Display */}\n          <div className=\"level-display\">\n            <div className=\"current-level\">\n              <span className=\"level-label\">Level:</span>\n              <span className=\"level-value\">{selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)}</span>\n            </div>\n            <div className=\"current-class\">\n              <span className=\"class-label\">Your Class:</span>\n              <span className=\"class-value\">\n                {user?.level === 'primary' ? `Class ${user?.class || 'N/A'}` :\n                 user?.level === 'secondary' ? `Form ${user?.class || 'N/A'}` :\n                 user?.level === 'advance' ? `Form ${user?.class || 'N/A'}` :\n                 'Not Set'}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"video-lessons-content\">\n        {/* Enhanced Filters and Controls */}\n        <div className=\"video-controls\">\n          <div className=\"controls-row\">\n            {/* Level Selector */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbSchool />\n                Level\n              </label>\n              <select\n                value={selectedLevel}\n                onChange={(e) => setSelectedLevel(e.target.value)}\n                className=\"control-select level-select\"\n              >\n                <option value=\"primary\">Primary (1-7)</option>\n                <option value=\"secondary\">Secondary (1-4)</option>\n                <option value=\"advance\">Advance (5-6)</option>\n              </select>\n            </div>\n\n            {/* Class Filter */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbFilter />\n                Filter by Class\n              </label>\n              <select\n                value={selectedClass}\n                onChange={(e) => setSelectedClass(e.target.value)}\n                className=\"control-select class-select\"\n              >\n                <option value=\"all\">All Classes</option>\n                {availableClasses.map((cls) => (\n                  <option key={cls} value={cls}>\n                    {selectedLevel === 'primary' ? `Class ${cls}` : `Form ${cls}`}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Subject Filter */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbFilter />\n                Subject\n              </label>\n              <select\n                value={selectedSubject}\n                onChange={(e) => setSelectedSubject(e.target.value)}\n                className=\"control-select subject-select\"\n              >\n                <option value=\"all\">All Subjects</option>\n                {availableSubjects.map((subject) => (\n                  <option key={subject} value={subject}>\n                    {subject}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Sort */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbSortAscending />\n                Sort\n              </label>\n              <select\n                value={sortBy}\n                onChange={(e) => setSortBy(e.target.value)}\n                className=\"control-select sort-select\"\n              >\n                <option value=\"newest\">Newest First</option>\n                <option value=\"oldest\">Oldest First</option>\n                <option value=\"title\">Title A-Z</option>\n                <option value=\"subject\">Subject A-Z</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Search Row */}\n          <div className=\"search-row\">\n            <div className=\"search-container\">\n              <TbSearch className=\"search-icon\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search videos by title, subject, or topic...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"search-input\"\n              />\n              {searchTerm && (\n                <button onClick={handleClearSearch} className=\"clear-search-btn\">\n                  <TbX />\n                  Clear Search\n                </button>\n              )}\n            </div>\n\n            <button onClick={handleRefresh} className=\"refresh-btn\">\n              <TbDownload />\n              Refresh All\n            </button>\n          </div>\n        </div>\n\n        {/* Loading State */}\n        {loading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>Loading videos...</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <TbAlertTriangle className=\"error-icon\" />\n            <h3>Error Loading Videos</h3>\n            <p>{error}</p>\n            <button onClick={fetchVideos} className=\"retry-btn\">\n              Try Again\n            </button>\n          </div>\n        ) : filteredAndSortedVideos.length > 0 ? (\n          <div className=\"videos-grid\">\n            {filteredAndSortedVideos.map((video, index) => (\n              <div key={index} className=\"video-card\" onClick={() => handleShowVideo(index)}>\n                <div className=\"video-card-thumbnail\">\n                  <img\n                    src={getThumbnailUrl(video)}\n                    alt={video.title}\n                    className=\"thumbnail-image\"\n                    onError={(e) => {\n                      // Fallback logic for failed thumbnails\n                      if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                        // For YouTube videos, try different quality thumbnails\n                        let videoId = video.videoID;\n                        if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                          const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                          videoId = match ? match[1] : videoId;\n                        }\n\n                        const fallbacks = [\n                          `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n                          `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,\n                          `https://img.youtube.com/vi/${videoId}/default.jpg`,\n                          '/api/placeholder/320/180'\n                        ];\n\n                        const currentSrc = e.target.src;\n                        const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n\n                        if (currentIndex < fallbacks.length - 1) {\n                          e.target.src = fallbacks[currentIndex + 1];\n                        }\n                      } else {\n                        e.target.src = '/api/placeholder/320/180';\n                      }\n                    }}\n                  />\n                  <div className=\"play-overlay\">\n                    <FaPlayCircle className=\"play-icon\" />\n                  </div>\n                  <div className=\"video-duration\">\n                    {video.duration || \"Video\"}\n                  </div>\n                  {video.subtitles && video.subtitles.length > 0 && (\n                    <div className=\"subtitle-badge\">\n                      <TbInfoCircle />\n                      CC\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"video-card-content\">\n                  <h3 className=\"video-title\">{video.title}</h3>\n                  <div className=\"video-meta\">\n                    <span className=\"video-subject\">{video.subject}</span>\n                    <span className=\"video-class\">\n                      {selectedLevel === 'primary' ? `Class ${video.className || video.class}` : `Form ${video.className || video.class}`}\n                    </span>\n                  </div>\n                  <div className=\"video-tags\">\n                    {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n                    {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (\n                      <span className=\"shared-tag\">\n                        Shared from {selectedLevel === 'primary' ? `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`}\n                      </span>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>No Videos Found</h3>\n            <p>No video lessons are available for your current selection.</p>\n            <p className=\"suggestion\">Try selecting a different class or subject.</p>\n          </div>\n        )}\n      </div>\n\n      {/* Enhanced Video Display */}\n      {showVideoIndices.length > 0 && currentVideoIndex !== null && (\n        <div className={`video-overlay ${isVideoExpanded ? 'expanded' : ''}`} onClick={(e) => {\n          if (e.target === e.currentTarget) handleHideVideo();\n        }}>\n          <div className={`video-modal ${isVideoExpanded ? 'expanded' : ''}`}>\n            {(() => {\n              const video = filteredAndSortedVideos[currentVideoIndex];\n              if (!video) return <div>Video not found</div>;\n\n              return (\n                <div className=\"video-content\">\n                  <div className=\"video-header\">\n                    <div className=\"video-info\">\n                      <h3 className=\"video-title\">{video.title}</h3>\n                      <div className=\"video-meta\">\n                        <span className=\"video-subject\">{video.subject}</span>\n                        <span className=\"video-class\">Class {video.className}</span>\n                        {video.level && <span className=\"video-level\">{video.level}</span>}\n                      </div>\n                    </div>\n                    <div className=\"video-controls\">\n                      <button\n                        className=\"control-btn expand-btn\"\n                        onClick={toggleVideoExpansion}\n                        title={isVideoExpanded ? \"Exit fullscreen\" : \"Enter fullscreen\"}\n                      >\n                        {isVideoExpanded ? <FaCompress /> : <FaExpand />}\n                      </button>\n                      <button\n                        className=\"control-btn close-btn\"\n                        onClick={handleHideVideo}\n                        title=\"Close video\"\n                      >\n                        <FaTimes />\n                      </button>\n                    </div>\n                  </div>\n                  <div className=\"video-container\">\n                    {video.videoUrl ? (\n                      <div style={{ padding: '15px', background: '#000', borderRadius: '8px' }}>\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"400\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '400px',\n                              backgroundColor: '#000'\n                            }}\n                            onError={(e) => {\n                              setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                            }}\n                            onCanPlay={() => {\n                              setVideoError(null);\n                            }}\n                            onLoadStart={() => {\n                              console.log('🎬 Video loading started');\n                            }}\n                            crossOrigin=\"anonymous\"\n                          >\n                            {/* Use signed URL if available, otherwise use original URL */}\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n\n                            {/* Add subtitle tracks if available */}\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n\n                            Your browser does not support the video tag.\n                          </video>\n\n                          {/* Subtitle indicator */}\n                          {video.subtitles && video.subtitles.length > 0 && (\n                            <div className=\"subtitle-indicator\">\n                              <TbInfoCircle className=\"subtitle-icon\" />\n                              <span>Subtitles available in {video.subtitles.length} language(s)</span>\n                            </div>\n                          )}\n\n                          {/* Video error display */}\n                          {videoError && (\n                            <div className=\"video-error-overlay\">\n                              <div className=\"error-content\">\n                                <TbAlertTriangle className=\"error-icon\" />\n                                <p>{videoError}</p>\n                                <button onClick={() => setVideoError(null)} className=\"dismiss-error-btn\">\n                                  Dismiss\n                                </button>\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                    ) : video.videoID ? (\n                      // Fallback to YouTube embed if no videoUrl\n                      <iframe\n                        src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                        title={video.title}\n                        frameBorder=\"0\"\n                        allowFullScreen\n                        className=\"video-iframe\"\n                        onLoad={() => console.log('✅ YouTube iframe loaded')}\n                      ></iframe>\n                    ) : (\n                      <div className=\"video-error\">\n                        <div className=\"error-icon\">⚠️</div>\n                        <h3>Video Unavailable</h3>\n                        <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                        <div className=\"error-actions\">\n                          <a\n                            href={video.signedVideoUrl || video.videoUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"external-link-btn\"\n                          >\n                            📱 Open in New Tab\n                          </a>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Comments Section */}\n                  {!isVideoExpanded && (\n                    <div className=\"comments-section\">\n                      <h4 className=\"comments-title\">\n                        <TbInfoCircle />\n                        Discussion ({comments.length})\n                      </h4>\n\n                      {/* Add Comment */}\n                      <div className=\"add-comment\">\n                        <div className=\"comment-input-container\">\n                          <textarea\n                            value={newComment}\n                            onChange={(e) => setNewComment(e.target.value)}\n                            placeholder=\"Share your thoughts about this video...\"\n                            className=\"comment-input\"\n                            rows=\"3\"\n                          />\n                          <button\n                            onClick={handleAddComment}\n                            className=\"comment-submit-btn\"\n                            disabled={!newComment.trim()}\n                          >\n                            Post Comment\n                          </button>\n                        </div>\n                      </div>\n\n                      {/* Comments List */}\n                      <div className=\"comments-list\">\n                        {comments.length === 0 ? (\n                          <div className=\"no-comments\">\n                            <TbInfoCircle />\n                            <p>No comments yet. Be the first to share your thoughts!</p>\n                          </div>\n                        ) : (\n                          comments.map((comment) => (\n                            <div key={comment.id} className=\"comment\">\n                              <div className=\"comment-header\">\n                                <span className=\"comment-author\">{comment.author}</span>\n                                <span className=\"comment-time\">\n                                  {new Date(comment.timestamp).toLocaleDateString()}\n                                </span>\n                              </div>\n                              <div className=\"comment-text\">{comment.text}</div>\n                              <div className=\"comment-actions\">\n                                <button\n                                  onClick={() => setReplyingTo(comment.id)}\n                                  className=\"reply-btn\"\n                                >\n                                  Reply\n                                </button>\n                              </div>\n\n                              {/* Reply Input */}\n                              {replyingTo === comment.id && (\n                                <div className=\"reply-input-container\">\n                                  <textarea\n                                    value={replyText}\n                                    onChange={(e) => setReplyText(e.target.value)}\n                                    placeholder=\"Write a reply...\"\n                                    className=\"reply-input\"\n                                    rows=\"2\"\n                                  />\n                                  <div className=\"reply-actions\">\n                                    <button\n                                      onClick={() => handleAddReply(comment.id)}\n                                      className=\"reply-submit-btn\"\n                                      disabled={!replyText.trim()}\n                                    >\n                                      Reply\n                                    </button>\n                                    <button\n                                      onClick={() => {\n                                        setReplyingTo(null);\n                                        setReplyText(\"\");\n                                      }}\n                                      className=\"reply-cancel-btn\"\n                                    >\n                                      Cancel\n                                    </button>\n                                  </div>\n                                </div>\n                              )}\n\n                              {/* Replies */}\n                              {comment.replies.length > 0 && (\n                                <div className=\"replies\">\n                                  {comment.replies.map((reply) => (\n                                    <div key={reply.id} className=\"reply\">\n                                      <div className=\"reply-header\">\n                                        <span className=\"reply-author\">{reply.author}</span>\n                                        <span className=\"reply-time\">\n                                          {new Date(reply.timestamp).toLocaleDateString()}\n                                        </span>\n                                      </div>\n                                      <div className=\"reply-text\">{reply.text}</div>\n                                    </div>\n                                  ))}\n                                </div>\n                              )}\n                            </div>\n                          ))\n                        )}\n                      </div>\n                    </div>\n                  )}\n                </div>\n              );\n            })()}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default VideoLessons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,OAAO,aAAa;AACpB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AAErE,SACEC,YAAY,EACZC,OAAO,EACPC,eAAe,EACfC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,aAAa,EACbC,QAAQ,EACRC,QAAQ,EACRC,UAAU,QACL,gBAAgB;AACvB,SACEC,OAAO,EACPC,QAAQ,EACRC,QAAQ,EACRC,QAAQ,EACRC,eAAe,EACfC,UAAU,EACVC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,aAAa,IAAIC,iBAAiB,EAClCC,WAAW,EACXC,GAAG,EACHC,eAAe,EACfC,YAAY,EACZC,OAAO,QACF,gBAAgB;AACvB,SAASC,eAAe,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjG,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGpC,WAAW,CAAEqC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAME,QAAQ,GAAGvC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACwC,MAAM,EAAEC,SAAS,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmD,KAAK,EAAEC,QAAQ,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqD,aAAa,EAAEC,gBAAgB,CAAC,GAAGtD,QAAQ,CAAC,CAAA4C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,KAAK,KAAI,SAAS,CAAC;EAC5E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzD,QAAQ,CAAC,CAAA4C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEc,KAAK,KAAI,KAAK,CAAC;EACxE,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6D,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+D,MAAM,EAAEC,SAAS,CAAC,GAAGhE,QAAQ,CAAC,QAAQ,CAAC;;EAE9C;EACA,MAAM,CAACiE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACmE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACqE,eAAe,EAAEC,kBAAkB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuE,UAAU,EAAEC,aAAa,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACyE,QAAQ,EAAEC,WAAW,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;;EAE9C;EACA,MAAM,CAAC2E,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC6E,UAAU,EAAEC,aAAa,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+E,UAAU,EAAEC,aAAa,CAAC,GAAGhF,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACiF,SAAS,EAAEC,YAAY,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAMmF,gBAAgB,GAAGhF,OAAO,CAAC,MAAM;IACrC,IAAIkD,aAAa,KAAK,SAAS,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC3E,IAAIA,aAAa,KAAK,WAAW,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC9D,IAAIA,aAAa,KAAK,SAAS,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IAClD,OAAO,EAAE;EACX,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAM+B,iBAAiB,GAAGjF,OAAO,CAAC,MAAM;IACtC,IAAIkD,aAAa,KAAK,SAAS,EAAE,OAAOhB,eAAe;IACvD,IAAIgB,aAAa,KAAK,WAAW,EAAE,OAAOf,iBAAiB;IAC3D,IAAIe,aAAa,KAAK,SAAS,EAAE,OAAOd,eAAe;IACvD,OAAO,EAAE;EACX,CAAC,EAAE,CAACc,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMgC,WAAW,GAAGnF,WAAW,CAAC,YAAY;IAC1C,IAAI;MAAA,IAAAoF,cAAA;MACFpC,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACdN,QAAQ,CAACpC,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAM6E,OAAO,GAAG;QACdhC,KAAK,EAAEF,aAAa;QACpBmC,SAAS,EAAE,EAAE;QAAE;QACfC,OAAO,EAAE,EAAE;QAAE;QACbC,OAAO,EAAE;MACX,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAMrF,gBAAgB,CAACiF,OAAO,CAAC;MAEhD,IAAII,QAAQ,aAARA,QAAQ,gBAAAL,cAAA,GAARK,QAAQ,CAAEC,IAAI,cAAAN,cAAA,eAAdA,cAAA,CAAgBO,OAAO,EAAE;QAC3B7C,SAAS,CAAC2C,QAAQ,CAACC,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MACrC,CAAC,MAAM;QAAA,IAAAE,eAAA;QACL1C,QAAQ,CAAC,CAAAuC,QAAQ,aAARA,QAAQ,wBAAAG,eAAA,GAARH,QAAQ,CAAEC,IAAI,cAAAE,eAAA,uBAAdA,eAAA,CAAgBC,OAAO,KAAI,wBAAwB,CAAC;QAC7D/C,SAAS,CAAC,EAAE,CAAC;MACf;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd6C,OAAO,CAAC7C,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CC,QAAQ,CAAC,0CAA0C,CAAC;MACpDJ,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;MACjBJ,QAAQ,CAACrC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAAC4C,aAAa,EAAEP,QAAQ,CAAC,CAAC;;EAE7B;EACA,MAAMmD,uBAAuB,GAAG9F,OAAO,CAAC,MAAM;IAC5C6F,OAAO,CAACE,GAAG,CAAC,0BAA0B,EAAE;MACtCC,WAAW,EAAEpD,MAAM,CAACqD,MAAM;MAC1B/C,aAAa;MACbG,aAAa;MACbG,eAAe;MACfE;IACF,CAAC,CAAC;IAEF,IAAIwC,QAAQ,GAAGtD,MAAM;;IAErB;IACA,MAAMuD,WAAW,GAAGD,QAAQ,CAACD,MAAM;IACnCC,QAAQ,GAAGA,QAAQ,CAACE,MAAM,CAACC,KAAK,IAAI;MAClC,MAAMC,OAAO,GAAGD,KAAK,CAACjD,KAAK,KAAKF,aAAa;MAC7C,IAAI,CAACoD,OAAO,EAAE;QACZT,OAAO,CAACE,GAAG,CAAC,0BAA0B,EAAEM,KAAK,CAACE,KAAK,EAAE,QAAQ,EAAEF,KAAK,CAACjD,KAAK,EAAE,WAAW,EAAEF,aAAa,CAAC;MACzG;MACA,OAAOoD,OAAO;IAChB,CAAC,CAAC;IACFT,OAAO,CAACE,GAAG,CAAE,oBAAmBI,WAAY,MAAKD,QAAQ,CAACD,MAAO,EAAC,CAAC;;IAEnE;IACA,IAAI5C,aAAa,KAAK,KAAK,EAAE;MAC3B,MAAMmD,WAAW,GAAGN,QAAQ,CAACD,MAAM;MACnCC,QAAQ,GAAGA,QAAQ,CAACE,MAAM,CAACC,KAAK,IAAI;QAClC;QACA,MAAMI,UAAU,GAAGJ,KAAK,CAAChB,SAAS,IAAIgB,KAAK,CAAC9C,KAAK;QACjD,MAAM+C,OAAO,GAAGG,UAAU,KAAKpD,aAAa;QAC5C,IAAI,CAACiD,OAAO,EAAE;UACZT,OAAO,CAACE,GAAG,CAAC,0BAA0B,EAAEM,KAAK,CAACE,KAAK,EAAE,QAAQ,EAAEE,UAAU,EAAE,WAAW,EAAEpD,aAAa,CAAC;QACxG;QACA,OAAOiD,OAAO;MAChB,CAAC,CAAC;MACFT,OAAO,CAACE,GAAG,CAAE,oBAAmBS,WAAY,MAAKN,QAAQ,CAACD,MAAO,EAAC,CAAC;IACrE;;IAEA;IACA,IAAIzC,eAAe,KAAK,KAAK,EAAE;MAC7B,MAAMkD,aAAa,GAAGR,QAAQ,CAACD,MAAM;MACrCC,QAAQ,GAAGA,QAAQ,CAACE,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACf,OAAO,KAAK9B,eAAe,CAAC;MACtEqC,OAAO,CAACE,GAAG,CAAE,sBAAqBW,aAAc,MAAKR,QAAQ,CAACD,MAAO,EAAC,CAAC;IACzE;;IAEA;IACA,IAAIvC,UAAU,CAACiD,IAAI,CAAC,CAAC,EAAE;MACrB,MAAMC,YAAY,GAAGV,QAAQ,CAACD,MAAM;MACpC,MAAMY,WAAW,GAAGnD,UAAU,CAACoD,WAAW,CAAC,CAAC;MAC5CZ,QAAQ,GAAGA,QAAQ,CAACE,MAAM,CAACC,KAAK;QAAA,IAAAU,YAAA,EAAAC,cAAA,EAAAC,YAAA;QAAA,OAC9B,EAAAF,YAAA,GAAAV,KAAK,CAACE,KAAK,cAAAQ,YAAA,uBAAXA,YAAA,CAAaD,WAAW,CAAC,CAAC,CAACI,QAAQ,CAACL,WAAW,CAAC,OAAAG,cAAA,GAChDX,KAAK,CAACf,OAAO,cAAA0B,cAAA,uBAAbA,cAAA,CAAeF,WAAW,CAAC,CAAC,CAACI,QAAQ,CAACL,WAAW,CAAC,OAAAI,YAAA,GAClDZ,KAAK,CAACc,KAAK,cAAAF,YAAA,uBAAXA,YAAA,CAAaH,WAAW,CAAC,CAAC,CAACI,QAAQ,CAACL,WAAW,CAAC;MAAA,CAClD,CAAC;MACDhB,OAAO,CAACE,GAAG,CAAE,qBAAoBa,YAAa,MAAKV,QAAQ,CAACD,MAAO,EAAC,CAAC;IACvE;;IAEA;IACA,MAAMmB,MAAM,GAAG,CAAC,GAAGlB,QAAQ,CAAC,CAACmB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC1C,QAAQ3D,MAAM;QACZ,KAAK,QAAQ;UACX,OAAO,IAAI4D,IAAI,CAACD,CAAC,CAACE,SAAS,IAAI,CAAC,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,IAAI,CAAC,CAAC;QAChE,KAAK,QAAQ;UACX,OAAO,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,IAAI,CAAC,CAAC,GAAG,IAAID,IAAI,CAACD,CAAC,CAACE,SAAS,IAAI,CAAC,CAAC;QAChE,KAAK,OAAO;UACV,OAAO,CAACH,CAAC,CAACf,KAAK,IAAI,EAAE,EAAEmB,aAAa,CAACH,CAAC,CAAChB,KAAK,IAAI,EAAE,CAAC;QACrD,KAAK,SAAS;UACZ,OAAO,CAACe,CAAC,CAAChC,OAAO,IAAI,EAAE,EAAEoC,aAAa,CAACH,CAAC,CAACjC,OAAO,IAAI,EAAE,CAAC;QACzD;UACE,OAAO,CAAC;MACZ;IACF,CAAC,CAAC;IAEFO,OAAO,CAACE,GAAG,CAAC,iBAAiB,EAAEqB,MAAM,CAACnB,MAAM,EAAE,QAAQ,CAAC;IACvD,OAAOmB,MAAM;EACf,CAAC,EAAE,CAACxE,MAAM,EAAEc,UAAU,EAAEE,MAAM,EAAEV,aAAa,EAAEG,aAAa,EAAEG,eAAe,CAAC,CAAC;;EAE/E;EACA,MAAMmE,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,MAAMvB,KAAK,GAAGP,uBAAuB,CAAC8B,KAAK,CAAC;IAE5C7D,oBAAoB,CAAC6D,KAAK,CAAC;IAC3B3D,mBAAmB,CAAC,CAAC2D,KAAK,CAAC,CAAC;IAC5BzD,kBAAkB,CAAC,KAAK,CAAC;IACzBE,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,IAAIgC,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEwB,QAAQ,KAAKxB,KAAK,CAACwB,QAAQ,CAACX,QAAQ,CAAC,eAAe,CAAC,IAAIb,KAAK,CAACwB,QAAQ,CAACX,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,IAAI;QACF,MAAMY,SAAS,GAAG,MAAMC,iBAAiB,CAAC1B,KAAK,CAACwB,QAAQ,CAAC;QACzDxB,KAAK,CAAC2B,cAAc,GAAGF,SAAS;MAClC,CAAC,CAAC,OAAO9E,KAAK,EAAE;QACd6C,OAAO,CAACoC,IAAI,CAAC,8CAA8C,CAAC;QAC5D5B,KAAK,CAAC2B,cAAc,GAAG3B,KAAK,CAACwB,QAAQ;MACvC;IACF;EACF,CAAC;EAED,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5BjE,mBAAmB,CAAC,EAAE,CAAC;IACvBF,oBAAoB,CAAC,IAAI,CAAC;IAC1BI,kBAAkB,CAAC,KAAK,CAAC;IACzBE,aAAa,CAAC,IAAI,CAAC;IACnB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAAC6D,KAAK,CAAC,CAAC;IAClB;EACF,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjCjE,kBAAkB,CAAC,CAACD,eAAe,CAAC;EACtC,CAAC;;EAED;EACA,MAAM6D,iBAAiB,GAAG,MAAOF,QAAQ,IAAK;IAC5C,IAAI,CAACA,QAAQ,EAAE,OAAOA,QAAQ;;IAE9B;IACA,IAAIA,QAAQ,CAACX,QAAQ,CAAC,eAAe,CAAC,IAAIW,QAAQ,CAACX,QAAQ,CAAC,KAAK,CAAC,EAAE;MAClE,IAAI;QACF,MAAM1B,QAAQ,GAAG,MAAM6C,KAAK,CAAE,6DAA4DC,kBAAkB,CAACT,QAAQ,CAAE,EAAC,EAAE;UACxHU,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAI,CAAChD,QAAQ,CAACiD,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAE,uBAAsBlD,QAAQ,CAACmD,MAAO,EAAC,CAAC;QAC3D;QAEA,MAAMlD,IAAI,GAAG,MAAMD,QAAQ,CAACoD,IAAI,CAAC,CAAC;QAElC,IAAInD,IAAI,CAACC,OAAO,IAAID,IAAI,CAACqC,SAAS,EAAE;UAClCjC,OAAO,CAACE,GAAG,CAAC,+BAA+B,CAAC;UAC5C,OAAON,IAAI,CAACqC,SAAS;QACvB,CAAC,MAAM;UACLjC,OAAO,CAACoC,IAAI,CAAC,+CAA+C,EAAExC,IAAI,CAAC;UACnE,OAAOoC,QAAQ;QACjB;MACF,CAAC,CAAC,OAAO7E,KAAK,EAAE;QACd6C,OAAO,CAAC7C,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,OAAO6E,QAAQ;MACjB;IACF;IAEA,OAAOA,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMgB,eAAe,GAAIxC,KAAK,IAAK;IACjC,IAAIA,KAAK,CAACyC,SAAS,EAAE;MACnB,OAAOzC,KAAK,CAACyC,SAAS;IACxB;IAEA,IAAIzC,KAAK,CAAC0C,OAAO,IAAI,CAAC1C,KAAK,CAAC0C,OAAO,CAAC7B,QAAQ,CAAC,eAAe,CAAC,EAAE;MAC7D,IAAI8B,OAAO,GAAG3C,KAAK,CAAC0C,OAAO;MAC3B,IAAIC,OAAO,CAAC9B,QAAQ,CAAC,aAAa,CAAC,IAAI8B,OAAO,CAAC9B,QAAQ,CAAC,UAAU,CAAC,EAAE;QACnE,MAAM+B,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;QACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;MACtC;MACA,OAAQ,8BAA6BA,OAAQ,oBAAmB;IAClE;IAEA,OAAO,0BAA0B;EACnC,CAAC;;EAED;EACAlJ,SAAS,CAAC,MAAM;IACdoF,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAEjBpF,SAAS,CAAC,MAAM;IACd,IAAI2C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEW,KAAK,EAAE;MACfD,gBAAgB,CAACV,IAAI,CAACW,KAAK,CAAC;IAC9B;IACA,IAAIX,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEc,KAAK,EAAE;MACfD,gBAAgB,CAACb,IAAI,CAACc,KAAK,CAAC;IAC9B;EACF,CAAC,EAAE,CAACd,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMyG,iBAAiB,GAAGA,CAAA,KAAM;IAC9BvF,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;EAED,MAAMwF,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACAjE,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMkE,cAAc,GAAGA,CAAA,KAAM;IAC3BzF,aAAa,CAAC,EAAE,CAAC;IACjBF,kBAAkB,CAAC,KAAK,CAAC;IACzBH,gBAAgB,CAAC,KAAK,CAAC;IACvB4B,WAAW,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAMmE,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI3E,UAAU,CAACiC,IAAI,CAAC,CAAC,EAAE;MACrB,MAAM2C,OAAO,GAAG;QACdC,EAAE,EAAE/B,IAAI,CAACgC,GAAG,CAAC,CAAC;QACdC,IAAI,EAAE/E,UAAU;QAChBgF,MAAM,EAAE,CAAAjH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkH,IAAI,KAAI,WAAW;QACjCC,SAAS,EAAE,IAAIpC,IAAI,CAAC,CAAC,CAACqC,WAAW,CAAC,CAAC;QACnCC,OAAO,EAAE;MACX,CAAC;MACDrF,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAE8E,OAAO,CAAC,CAAC;MACnC3E,aAAa,CAAC,EAAE,CAAC;IACnB;EACF,CAAC;EAED,MAAMoF,cAAc,GAAIC,SAAS,IAAK;IACpC,IAAIlF,SAAS,CAAC6B,IAAI,CAAC,CAAC,EAAE;MACpB,MAAMsD,KAAK,GAAG;QACZV,EAAE,EAAE/B,IAAI,CAACgC,GAAG,CAAC,CAAC;QACdC,IAAI,EAAE3E,SAAS;QACf4E,MAAM,EAAE,CAAAjH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkH,IAAI,KAAI,WAAW;QACjCC,SAAS,EAAE,IAAIpC,IAAI,CAAC,CAAC,CAACqC,WAAW,CAAC;MACpC,CAAC;MAEDpF,WAAW,CAACD,QAAQ,CAAC0F,GAAG,CAACZ,OAAO,IAC9BA,OAAO,CAACC,EAAE,KAAKS,SAAS,GACpB;QAAE,GAAGV,OAAO;QAAEQ,OAAO,EAAE,CAAC,GAAGR,OAAO,CAACQ,OAAO,EAAEG,KAAK;MAAE,CAAC,GACpDX,OACN,CAAC,CAAC;MACFvE,YAAY,CAAC,EAAE,CAAC;MAChBF,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,oBACEvC,OAAA;IAAK+C,SAAS,EAAC,yBAAyB;IAAA8E,QAAA,gBAEtC7H,OAAA;MAAK+C,SAAS,EAAC,sBAAsB;MAAA8E,QAAA,eACnC7H,OAAA;QAAK+C,SAAS,EAAC,gBAAgB;QAAA8E,QAAA,gBAC7B7H,OAAA;UAAK+C,SAAS,EAAC,aAAa;UAAA8E,QAAA,gBAC1B7H,OAAA;YAAK+C,SAAS,EAAC,aAAa;YAAA8E,QAAA,eAC1B7H,OAAA,CAACpB,OAAO;cAAAkJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACNjI,OAAA;YAAK+C,SAAS,EAAC,aAAa;YAAA8E,QAAA,gBAC1B7H,OAAA;cAAA6H,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBjI,OAAA;cAAA6H,QAAA,EAAG;YAAiD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNjI,OAAA;UAAK+C,SAAS,EAAC,eAAe;UAAA8E,QAAA,gBAC5B7H,OAAA;YAAK+C,SAAS,EAAC,eAAe;YAAA8E,QAAA,gBAC5B7H,OAAA;cAAM+C,SAAS,EAAC,aAAa;cAAA8E,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3CjI,OAAA;cAAM+C,SAAS,EAAC,aAAa;cAAA8E,QAAA,EAAEjH,aAAa,CAACsH,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGvH,aAAa,CAACwH,KAAK,CAAC,CAAC;YAAC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC,eACNjI,OAAA;YAAK+C,SAAS,EAAC,eAAe;YAAA8E,QAAA,gBAC5B7H,OAAA;cAAM+C,SAAS,EAAC,aAAa;cAAA8E,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDjI,OAAA;cAAM+C,SAAS,EAAC,aAAa;cAAA8E,QAAA,EAC1B,CAAA1H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,KAAK,MAAK,SAAS,GAAI,SAAQ,CAAAX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEc,KAAK,KAAI,KAAM,EAAC,GAC3D,CAAAd,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,KAAK,MAAK,WAAW,GAAI,QAAO,CAAAX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEc,KAAK,KAAI,KAAM,EAAC,GAC5D,CAAAd,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,KAAK,MAAK,SAAS,GAAI,QAAO,CAAAX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEc,KAAK,KAAI,KAAM,EAAC,GAC1D;YAAS;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENjI,OAAA;MAAK+C,SAAS,EAAC,uBAAuB;MAAA8E,QAAA,gBAEpC7H,OAAA;QAAK+C,SAAS,EAAC,gBAAgB;QAAA8E,QAAA,gBAC7B7H,OAAA;UAAK+C,SAAS,EAAC,cAAc;UAAA8E,QAAA,gBAE3B7H,OAAA;YAAK+C,SAAS,EAAC,eAAe;YAAA8E,QAAA,gBAC5B7H,OAAA;cAAO+C,SAAS,EAAC,eAAe;cAAA8E,QAAA,gBAC9B7H,OAAA,CAACnB,QAAQ;gBAAAiJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,SAEd;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjI,OAAA;cACEqI,KAAK,EAAEzH,aAAc;cACrB0H,QAAQ,EAAGC,CAAC,IAAK1H,gBAAgB,CAAC0H,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAClDtF,SAAS,EAAC,6BAA6B;cAAA8E,QAAA,gBAEvC7H,OAAA;gBAAQqI,KAAK,EAAC,SAAS;gBAAAR,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9CjI,OAAA;gBAAQqI,KAAK,EAAC,WAAW;gBAAAR,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClDjI,OAAA;gBAAQqI,KAAK,EAAC,SAAS;gBAAAR,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNjI,OAAA;YAAK+C,SAAS,EAAC,eAAe;YAAA8E,QAAA,gBAC5B7H,OAAA;cAAO+C,SAAS,EAAC,eAAe;cAAA8E,QAAA,gBAC9B7H,OAAA,CAACjB,QAAQ;gBAAA+I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAEd;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjI,OAAA;cACEqI,KAAK,EAAEtH,aAAc;cACrBuH,QAAQ,EAAGC,CAAC,IAAKvH,gBAAgB,CAACuH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAClDtF,SAAS,EAAC,6BAA6B;cAAA8E,QAAA,gBAEvC7H,OAAA;gBAAQqI,KAAK,EAAC,KAAK;gBAAAR,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACvCvF,gBAAgB,CAACkF,GAAG,CAAEa,GAAG,iBACxBzI,OAAA;gBAAkBqI,KAAK,EAAEI,GAAI;gBAAAZ,QAAA,EAC1BjH,aAAa,KAAK,SAAS,GAAI,SAAQ6H,GAAI,EAAC,GAAI,QAAOA,GAAI;cAAC,GADlDA,GAAG;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAER,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNjI,OAAA;YAAK+C,SAAS,EAAC,eAAe;YAAA8E,QAAA,gBAC5B7H,OAAA;cAAO+C,SAAS,EAAC,eAAe;cAAA8E,QAAA,gBAC9B7H,OAAA,CAACjB,QAAQ;gBAAA+I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEd;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjI,OAAA;cACEqI,KAAK,EAAEnH,eAAgB;cACvBoH,QAAQ,EAAGC,CAAC,IAAKpH,kBAAkB,CAACoH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACpDtF,SAAS,EAAC,+BAA+B;cAAA8E,QAAA,gBAEzC7H,OAAA;gBAAQqI,KAAK,EAAC,KAAK;gBAAAR,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACxCtF,iBAAiB,CAACiF,GAAG,CAAE5E,OAAO,iBAC7BhD,OAAA;gBAAsBqI,KAAK,EAAErF,OAAQ;gBAAA6E,QAAA,EAClC7E;cAAO,GADGA,OAAO;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEZ,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNjI,OAAA;YAAK+C,SAAS,EAAC,eAAe;YAAA8E,QAAA,gBAC5B7H,OAAA;cAAO+C,SAAS,EAAC,eAAe;cAAA8E,QAAA,gBAC9B7H,OAAA,CAAChB,eAAe;gBAAA8I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,QAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjI,OAAA;cACEqI,KAAK,EAAE/G,MAAO;cACdgH,QAAQ,EAAGC,CAAC,IAAKhH,SAAS,CAACgH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC3CtF,SAAS,EAAC,4BAA4B;cAAA8E,QAAA,gBAEtC7H,OAAA;gBAAQqI,KAAK,EAAC,QAAQ;gBAAAR,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CjI,OAAA;gBAAQqI,KAAK,EAAC,QAAQ;gBAAAR,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CjI,OAAA;gBAAQqI,KAAK,EAAC,OAAO;gBAAAR,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCjI,OAAA;gBAAQqI,KAAK,EAAC,SAAS;gBAAAR,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNjI,OAAA;UAAK+C,SAAS,EAAC,YAAY;UAAA8E,QAAA,gBACzB7H,OAAA;YAAK+C,SAAS,EAAC,kBAAkB;YAAA8E,QAAA,gBAC/B7H,OAAA,CAAClB,QAAQ;cAACiE,SAAS,EAAC;YAAa;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpCjI,OAAA;cACE0I,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,8CAA8C;cAC1DN,KAAK,EAAEjH,UAAW;cAClBkH,QAAQ,EAAGC,CAAC,IAAKlH,aAAa,CAACkH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/CtF,SAAS,EAAC;YAAc;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,EACD7G,UAAU,iBACTpB,OAAA;cAAQ4I,OAAO,EAAEhC,iBAAkB;cAAC7D,SAAS,EAAC,kBAAkB;cAAA8E,QAAA,gBAC9D7H,OAAA,CAACR,GAAG;gBAAAsI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAET;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENjI,OAAA;YAAQ4I,OAAO,EAAE/B,aAAc;YAAC9D,SAAS,EAAC,aAAa;YAAA8E,QAAA,gBACrD7H,OAAA,CAACf,UAAU;cAAA6I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEhB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLzH,OAAO,gBACNR,OAAA;QAAK+C,SAAS,EAAC,eAAe;QAAA8E,QAAA,gBAC5B7H,OAAA;UAAK+C,SAAS,EAAC;QAAiB;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCjI,OAAA;UAAA6H,QAAA,EAAG;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,GACJvH,KAAK,gBACPV,OAAA;QAAK+C,SAAS,EAAC,aAAa;QAAA8E,QAAA,gBAC1B7H,OAAA,CAACP,eAAe;UAACsD,SAAS,EAAC;QAAY;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CjI,OAAA;UAAA6H,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BjI,OAAA;UAAA6H,QAAA,EAAInH;QAAK;UAAAoH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdjI,OAAA;UAAQ4I,OAAO,EAAEhG,WAAY;UAACG,SAAS,EAAC,WAAW;UAAA8E,QAAA,EAAC;QAEpD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJzE,uBAAuB,CAACG,MAAM,GAAG,CAAC,gBACpC3D,OAAA;QAAK+C,SAAS,EAAC,aAAa;QAAA8E,QAAA,EACzBrE,uBAAuB,CAACoE,GAAG,CAAC,CAAC7D,KAAK,EAAEuB,KAAK,kBACxCtF,OAAA;UAAiB+C,SAAS,EAAC,YAAY;UAAC6F,OAAO,EAAEA,CAAA,KAAMvD,eAAe,CAACC,KAAK,CAAE;UAAAuC,QAAA,gBAC5E7H,OAAA;YAAK+C,SAAS,EAAC,sBAAsB;YAAA8E,QAAA,gBACnC7H,OAAA;cACE6I,GAAG,EAAEtC,eAAe,CAACxC,KAAK,CAAE;cAC5B+E,GAAG,EAAE/E,KAAK,CAACE,KAAM;cACjBlB,SAAS,EAAC,iBAAiB;cAC3BgG,OAAO,EAAGR,CAAC,IAAK;gBACd;gBACA,IAAIxE,KAAK,CAAC0C,OAAO,IAAI,CAAC1C,KAAK,CAAC0C,OAAO,CAAC7B,QAAQ,CAAC,eAAe,CAAC,EAAE;kBAC7D;kBACA,IAAI8B,OAAO,GAAG3C,KAAK,CAAC0C,OAAO;kBAC3B,IAAIC,OAAO,CAAC9B,QAAQ,CAAC,aAAa,CAAC,IAAI8B,OAAO,CAAC9B,QAAQ,CAAC,UAAU,CAAC,EAAE;oBACnE,MAAM+B,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;oBACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;kBACtC;kBAEA,MAAMsC,SAAS,GAAG,CACf,8BAA6BtC,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,cAAa,EACnD,0BAA0B,CAC3B;kBAED,MAAMuC,UAAU,GAAGV,CAAC,CAACC,MAAM,CAACK,GAAG;kBAC/B,MAAMK,YAAY,GAAGF,SAAS,CAACG,SAAS,CAACC,GAAG,IAAIH,UAAU,CAACrE,QAAQ,CAACwE,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;kBAE1F,IAAIJ,YAAY,GAAGF,SAAS,CAACrF,MAAM,GAAG,CAAC,EAAE;oBACvC4E,CAAC,CAACC,MAAM,CAACK,GAAG,GAAGG,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;kBAC5C;gBACF,CAAC,MAAM;kBACLX,CAAC,CAACC,MAAM,CAACK,GAAG,GAAG,0BAA0B;gBAC3C;cACF;YAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFjI,OAAA;cAAK+C,SAAS,EAAC,cAAc;cAAA8E,QAAA,eAC3B7H,OAAA,CAAC9B,YAAY;gBAAC6E,SAAS,EAAC;cAAW;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACNjI,OAAA;cAAK+C,SAAS,EAAC,gBAAgB;cAAA8E,QAAA,EAC5B9D,KAAK,CAACwF,QAAQ,IAAI;YAAO;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,EACLlE,KAAK,CAACyF,SAAS,IAAIzF,KAAK,CAACyF,SAAS,CAAC7F,MAAM,GAAG,CAAC,iBAC5C3D,OAAA;cAAK+C,SAAS,EAAC,gBAAgB;cAAA8E,QAAA,gBAC7B7H,OAAA,CAACN,YAAY;gBAAAoI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,MAElB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENjI,OAAA;YAAK+C,SAAS,EAAC,oBAAoB;YAAA8E,QAAA,gBACjC7H,OAAA;cAAI+C,SAAS,EAAC,aAAa;cAAA8E,QAAA,EAAE9D,KAAK,CAACE;YAAK;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9CjI,OAAA;cAAK+C,SAAS,EAAC,YAAY;cAAA8E,QAAA,gBACzB7H,OAAA;gBAAM+C,SAAS,EAAC,eAAe;gBAAA8E,QAAA,EAAE9D,KAAK,CAACf;cAAO;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtDjI,OAAA;gBAAM+C,SAAS,EAAC,aAAa;gBAAA8E,QAAA,EAC1BjH,aAAa,KAAK,SAAS,GAAI,SAAQmD,KAAK,CAAChB,SAAS,IAAIgB,KAAK,CAAC9C,KAAM,EAAC,GAAI,QAAO8C,KAAK,CAAChB,SAAS,IAAIgB,KAAK,CAAC9C,KAAM;cAAC;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNjI,OAAA;cAAK+C,SAAS,EAAC,YAAY;cAAA8E,QAAA,GACxB9D,KAAK,CAACc,KAAK,iBAAI7E,OAAA;gBAAM+C,SAAS,EAAC,WAAW;gBAAA8E,QAAA,EAAE9D,KAAK,CAACc;cAAK;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC/DlE,KAAK,CAAC0F,eAAe,IAAI1F,KAAK,CAAC0F,eAAe,MAAM1F,KAAK,CAAChB,SAAS,IAAIgB,KAAK,CAAC9C,KAAK,CAAC,iBAClFjB,OAAA;gBAAM+C,SAAS,EAAC,YAAY;gBAAA8E,QAAA,GAAC,cACf,EAACjH,aAAa,KAAK,SAAS,GAAI,SAAQmD,KAAK,CAAC0F,eAAgB,EAAC,GAAI,QAAO1F,KAAK,CAAC0F,eAAgB,EAAC;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzG,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAhEE3C,KAAK;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiEV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENjI,OAAA;QAAK+C,SAAS,EAAC,aAAa;QAAA8E,QAAA,gBAC1B7H,OAAA,CAAC5B,eAAe;UAAC2E,SAAS,EAAC;QAAY;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CjI,OAAA;UAAA6H,QAAA,EAAI;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBjI,OAAA;UAAA6H,QAAA,EAAG;QAA0D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACjEjI,OAAA;UAAG+C,SAAS,EAAC,YAAY;UAAA8E,QAAA,EAAC;QAA2C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLvG,gBAAgB,CAACiC,MAAM,GAAG,CAAC,IAAInC,iBAAiB,KAAK,IAAI,iBACxDxB,OAAA;MAAK+C,SAAS,EAAG,iBAAgBnB,eAAe,GAAG,UAAU,GAAG,EAAG,EAAE;MAACgH,OAAO,EAAGL,CAAC,IAAK;QACpF,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACmB,aAAa,EAAE9D,eAAe,CAAC,CAAC;MACrD,CAAE;MAAAiC,QAAA,eACA7H,OAAA;QAAK+C,SAAS,EAAG,eAAcnB,eAAe,GAAG,UAAU,GAAG,EAAG,EAAE;QAAAiG,QAAA,EAChE,CAAC,MAAM;UACN,MAAM9D,KAAK,GAAGP,uBAAuB,CAAChC,iBAAiB,CAAC;UACxD,IAAI,CAACuC,KAAK,EAAE,oBAAO/D,OAAA;YAAA6H,QAAA,EAAK;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;UAE7C,oBACEjI,OAAA;YAAK+C,SAAS,EAAC,eAAe;YAAA8E,QAAA,gBAC5B7H,OAAA;cAAK+C,SAAS,EAAC,cAAc;cAAA8E,QAAA,gBAC3B7H,OAAA;gBAAK+C,SAAS,EAAC,YAAY;gBAAA8E,QAAA,gBACzB7H,OAAA;kBAAI+C,SAAS,EAAC,aAAa;kBAAA8E,QAAA,EAAE9D,KAAK,CAACE;gBAAK;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9CjI,OAAA;kBAAK+C,SAAS,EAAC,YAAY;kBAAA8E,QAAA,gBACzB7H,OAAA;oBAAM+C,SAAS,EAAC,eAAe;oBAAA8E,QAAA,EAAE9D,KAAK,CAACf;kBAAO;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtDjI,OAAA;oBAAM+C,SAAS,EAAC,aAAa;oBAAA8E,QAAA,GAAC,QAAM,EAAC9D,KAAK,CAAChB,SAAS;kBAAA;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC3DlE,KAAK,CAACjD,KAAK,iBAAId,OAAA;oBAAM+C,SAAS,EAAC,aAAa;oBAAA8E,QAAA,EAAE9D,KAAK,CAACjD;kBAAK;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNjI,OAAA;gBAAK+C,SAAS,EAAC,gBAAgB;gBAAA8E,QAAA,gBAC7B7H,OAAA;kBACE+C,SAAS,EAAC,wBAAwB;kBAClC6F,OAAO,EAAE9C,oBAAqB;kBAC9B7B,KAAK,EAAErC,eAAe,GAAG,iBAAiB,GAAG,kBAAmB;kBAAAiG,QAAA,EAE/DjG,eAAe,gBAAG5B,OAAA,CAACrB,UAAU;oBAAAmJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGjI,OAAA,CAACtB,QAAQ;oBAAAoJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eACTjI,OAAA;kBACE+C,SAAS,EAAC,uBAAuB;kBACjC6F,OAAO,EAAEhD,eAAgB;kBACzB3B,KAAK,EAAC,aAAa;kBAAA4D,QAAA,eAEnB7H,OAAA,CAACzB,OAAO;oBAAAuJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjI,OAAA;cAAK+C,SAAS,EAAC,iBAAiB;cAAA8E,QAAA,EAC7B9D,KAAK,CAACwB,QAAQ,gBACbvF,OAAA;gBAAK2J,KAAK,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,MAAM;kBAAEC,YAAY,EAAE;gBAAM,CAAE;gBAAAjC,QAAA,gBACrE7H,OAAA;kBACE+J,GAAG,EAAGA,GAAG,IAAK9H,WAAW,CAAC8H,GAAG,CAAE;kBAC/BC,QAAQ;kBACRC,QAAQ;kBACRC,WAAW;kBACXC,OAAO,EAAC,UAAU;kBAClBC,KAAK,EAAC,MAAM;kBACZC,MAAM,EAAC,KAAK;kBACZC,MAAM,EAAE/D,eAAe,CAACxC,KAAK,CAAE;kBAC/B4F,KAAK,EAAE;oBACLS,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,OAAO;oBACfE,eAAe,EAAE;kBACnB,CAAE;kBACFxB,OAAO,EAAGR,CAAC,IAAK;oBACdxG,aAAa,CAAE,yBAAwBgC,KAAK,CAACE,KAAM,mCAAkC,CAAC;kBACxF,CAAE;kBACFuG,SAAS,EAAEA,CAAA,KAAM;oBACfzI,aAAa,CAAC,IAAI,CAAC;kBACrB,CAAE;kBACF0I,WAAW,EAAEA,CAAA,KAAM;oBACjBlH,OAAO,CAACE,GAAG,CAAC,0BAA0B,CAAC;kBACzC,CAAE;kBACFiH,WAAW,EAAC,WAAW;kBAAA7C,QAAA,gBAGvB7H,OAAA;oBAAQ6I,GAAG,EAAE9E,KAAK,CAAC2B,cAAc,IAAI3B,KAAK,CAACwB,QAAS;oBAACmD,IAAI,EAAC;kBAAW;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAGvElE,KAAK,CAACyF,SAAS,IAAIzF,KAAK,CAACyF,SAAS,CAAC7F,MAAM,GAAG,CAAC,IAAII,KAAK,CAACyF,SAAS,CAAC5B,GAAG,CAAC,CAAC+C,QAAQ,EAAErF,KAAK,kBACpFtF,OAAA;oBAEE4K,IAAI,EAAC,WAAW;oBAChB/B,GAAG,EAAE8B,QAAQ,CAACvB,GAAI;oBAClByB,OAAO,EAAEF,QAAQ,CAACG,QAAS;oBAC3BC,KAAK,EAAEJ,QAAQ,CAACK,YAAa;oBAC7BC,OAAO,EAAEN,QAAQ,CAACO,SAAS,IAAI5F,KAAK,KAAK;kBAAE,GALrC,GAAEqF,QAAQ,CAACG,QAAS,IAAGxF,KAAM,EAAC;oBAAAwC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAMrC,CACF,CAAC,EAAC,8CAGL;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAGPlE,KAAK,CAACyF,SAAS,IAAIzF,KAAK,CAACyF,SAAS,CAAC7F,MAAM,GAAG,CAAC,iBAC5C3D,OAAA;kBAAK+C,SAAS,EAAC,oBAAoB;kBAAA8E,QAAA,gBACjC7H,OAAA,CAACN,YAAY;oBAACqD,SAAS,EAAC;kBAAe;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1CjI,OAAA;oBAAA6H,QAAA,GAAM,yBAAuB,EAAC9D,KAAK,CAACyF,SAAS,CAAC7F,MAAM,EAAC,cAAY;kBAAA;oBAAAmE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CACN,EAGAnG,UAAU,iBACT9B,OAAA;kBAAK+C,SAAS,EAAC,qBAAqB;kBAAA8E,QAAA,eAClC7H,OAAA;oBAAK+C,SAAS,EAAC,eAAe;oBAAA8E,QAAA,gBAC5B7H,OAAA,CAACP,eAAe;sBAACsD,SAAS,EAAC;oBAAY;sBAAA+E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1CjI,OAAA;sBAAA6H,QAAA,EAAI/F;oBAAU;sBAAAgG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnBjI,OAAA;sBAAQ4I,OAAO,EAAEA,CAAA,KAAM7G,aAAa,CAAC,IAAI,CAAE;sBAACgB,SAAS,EAAC,mBAAmB;sBAAA8E,QAAA,EAAC;oBAE1E;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,GACNlE,KAAK,CAAC0C,OAAO;cAAA;cACf;cACAzG,OAAA;gBACE6I,GAAG,EAAG,iCAAgC9E,KAAK,CAAC0C,OAAQ,mBAAmB;gBACvExC,KAAK,EAAEF,KAAK,CAACE,KAAM;gBACnBkH,WAAW,EAAC,GAAG;gBACfC,eAAe;gBACfrI,SAAS,EAAC,cAAc;gBACxBsI,MAAM,EAAEA,CAAA,KAAM9H,OAAO,CAACE,GAAG,CAAC,yBAAyB;cAAE;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,gBAEVjI,OAAA;gBAAK+C,SAAS,EAAC,aAAa;gBAAA8E,QAAA,gBAC1B7H,OAAA;kBAAK+C,SAAS,EAAC,YAAY;kBAAA8E,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpCjI,OAAA;kBAAA6H,QAAA,EAAI;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1BjI,OAAA;kBAAA6H,QAAA,EAAI/F,UAAU,IAAI;gBAA4C;kBAAAgG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnEjI,OAAA;kBAAK+C,SAAS,EAAC,eAAe;kBAAA8E,QAAA,eAC5B7H,OAAA;oBACEsL,IAAI,EAAEvH,KAAK,CAAC2B,cAAc,IAAI3B,KAAK,CAACwB,QAAS;oBAC7CiD,MAAM,EAAC,QAAQ;oBACf+C,GAAG,EAAC,qBAAqB;oBACzBxI,SAAS,EAAC,mBAAmB;oBAAA8E,QAAA,EAC9B;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGL,CAACrG,eAAe,iBACf5B,OAAA;cAAK+C,SAAS,EAAC,kBAAkB;cAAA8E,QAAA,gBAC/B7H,OAAA;gBAAI+C,SAAS,EAAC,gBAAgB;gBAAA8E,QAAA,gBAC5B7H,OAAA,CAACN,YAAY;kBAAAoI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBACJ,EAAC/F,QAAQ,CAACyB,MAAM,EAAC,GAC/B;cAAA;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAGLjI,OAAA;gBAAK+C,SAAS,EAAC,aAAa;gBAAA8E,QAAA,eAC1B7H,OAAA;kBAAK+C,SAAS,EAAC,yBAAyB;kBAAA8E,QAAA,gBACtC7H,OAAA;oBACEqI,KAAK,EAAEjG,UAAW;oBAClBkG,QAAQ,EAAGC,CAAC,IAAKlG,aAAa,CAACkG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAC/CM,WAAW,EAAC,yCAAyC;oBACrD5F,SAAS,EAAC,eAAe;oBACzByI,IAAI,EAAC;kBAAG;oBAAA1D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACFjI,OAAA;oBACE4I,OAAO,EAAE7B,gBAAiB;oBAC1BhE,SAAS,EAAC,oBAAoB;oBAC9B0I,QAAQ,EAAE,CAACrJ,UAAU,CAACiC,IAAI,CAAC,CAAE;oBAAAwD,QAAA,EAC9B;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNjI,OAAA;gBAAK+C,SAAS,EAAC,eAAe;gBAAA8E,QAAA,EAC3B3F,QAAQ,CAACyB,MAAM,KAAK,CAAC,gBACpB3D,OAAA;kBAAK+C,SAAS,EAAC,aAAa;kBAAA8E,QAAA,gBAC1B7H,OAAA,CAACN,YAAY;oBAAAoI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChBjI,OAAA;oBAAA6H,QAAA,EAAG;kBAAqD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC,GAEN/F,QAAQ,CAAC0F,GAAG,CAAEZ,OAAO,iBACnBhH,OAAA;kBAAsB+C,SAAS,EAAC,SAAS;kBAAA8E,QAAA,gBACvC7H,OAAA;oBAAK+C,SAAS,EAAC,gBAAgB;oBAAA8E,QAAA,gBAC7B7H,OAAA;sBAAM+C,SAAS,EAAC,gBAAgB;sBAAA8E,QAAA,EAAEb,OAAO,CAACI;oBAAM;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACxDjI,OAAA;sBAAM+C,SAAS,EAAC,cAAc;sBAAA8E,QAAA,EAC3B,IAAI3C,IAAI,CAAC8B,OAAO,CAACM,SAAS,CAAC,CAACoE,kBAAkB,CAAC;oBAAC;sBAAA5D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNjI,OAAA;oBAAK+C,SAAS,EAAC,cAAc;oBAAA8E,QAAA,EAAEb,OAAO,CAACG;kBAAI;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClDjI,OAAA;oBAAK+C,SAAS,EAAC,iBAAiB;oBAAA8E,QAAA,eAC9B7H,OAAA;sBACE4I,OAAO,EAAEA,CAAA,KAAMrG,aAAa,CAACyE,OAAO,CAACC,EAAE,CAAE;sBACzClE,SAAS,EAAC,WAAW;sBAAA8E,QAAA,EACtB;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,EAGL3F,UAAU,KAAK0E,OAAO,CAACC,EAAE,iBACxBjH,OAAA;oBAAK+C,SAAS,EAAC,uBAAuB;oBAAA8E,QAAA,gBACpC7H,OAAA;sBACEqI,KAAK,EAAE7F,SAAU;sBACjB8F,QAAQ,EAAGC,CAAC,IAAK9F,YAAY,CAAC8F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;sBAC9CM,WAAW,EAAC,kBAAkB;sBAC9B5F,SAAS,EAAC,aAAa;sBACvByI,IAAI,EAAC;oBAAG;sBAAA1D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC,eACFjI,OAAA;sBAAK+C,SAAS,EAAC,eAAe;sBAAA8E,QAAA,gBAC5B7H,OAAA;wBACE4I,OAAO,EAAEA,CAAA,KAAMnB,cAAc,CAACT,OAAO,CAACC,EAAE,CAAE;wBAC1ClE,SAAS,EAAC,kBAAkB;wBAC5B0I,QAAQ,EAAE,CAACjJ,SAAS,CAAC6B,IAAI,CAAC,CAAE;wBAAAwD,QAAA,EAC7B;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACTjI,OAAA;wBACE4I,OAAO,EAAEA,CAAA,KAAM;0BACbrG,aAAa,CAAC,IAAI,CAAC;0BACnBE,YAAY,CAAC,EAAE,CAAC;wBAClB,CAAE;wBACFM,SAAS,EAAC,kBAAkB;wBAAA8E,QAAA,EAC7B;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,EAGAjB,OAAO,CAACQ,OAAO,CAAC7D,MAAM,GAAG,CAAC,iBACzB3D,OAAA;oBAAK+C,SAAS,EAAC,SAAS;oBAAA8E,QAAA,EACrBb,OAAO,CAACQ,OAAO,CAACI,GAAG,CAAED,KAAK,iBACzB3H,OAAA;sBAAoB+C,SAAS,EAAC,OAAO;sBAAA8E,QAAA,gBACnC7H,OAAA;wBAAK+C,SAAS,EAAC,cAAc;wBAAA8E,QAAA,gBAC3B7H,OAAA;0BAAM+C,SAAS,EAAC,cAAc;0BAAA8E,QAAA,EAAEF,KAAK,CAACP;wBAAM;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eACpDjI,OAAA;0BAAM+C,SAAS,EAAC,YAAY;0BAAA8E,QAAA,EACzB,IAAI3C,IAAI,CAACyC,KAAK,CAACL,SAAS,CAAC,CAACoE,kBAAkB,CAAC;wBAAC;0BAAA5D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3C,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNjI,OAAA;wBAAK+C,SAAS,EAAC,YAAY;wBAAA8E,QAAA,EAAEF,KAAK,CAACR;sBAAI;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA,GAPtCN,KAAK,CAACV,EAAE;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAQb,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CACN;gBAAA,GA/DOjB,OAAO,CAACC,EAAE;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgEf,CACN;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAEV,CAAC,EAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAC/H,EAAA,CAvxBQD,YAAY;EAAA,QACFlC,WAAW,EACXD,WAAW;AAAA;AAAA6N,EAAA,GAFrB1L,YAAY;AAyxBrB,eAAeA,YAAY;AAAC,IAAA0L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}