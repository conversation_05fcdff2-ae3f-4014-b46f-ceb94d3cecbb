{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { FaPlayCircle, FaVideo, FaGraduationCap, FaDownload, FaEye, FaTimes, FaChevronDown, FaSearch, FaExpand, FaCompress } from \"react-icons/fa\";\nimport { TbVideo, TbSchool, <PERSON>b<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Tb<PERSON>ortAscending, Tb<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON><PERSON>, TbChevronDown as TbChevronDownIcon, TbChevronUp, TbX, TbAlertTriangle, TbInfoCircle, TbCheck } from \"react-icons/tb\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction VideoLessons() {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const dispatch = useDispatch();\n\n  // State management\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedLevel, setSelectedLevel] = useState((user === null || user === void 0 ? void 0 : user.level) || \"primary\");\n  const [selectedClass, setSelectedClass] = useState(\"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Video player state\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [videoError, setVideoError] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n\n  // Comments state\n  const [comments, setComments] = useState([]);\n  const [newComment, setNewComment] = useState(\"\");\n  const [replyingTo, setReplyingTo] = useState(null);\n  const [replyText, setReplyText] = useState(\"\");\n\n  // Available classes based on level\n  const availableClasses = useMemo(() => {\n    if (selectedLevel === \"primary\") return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    if (selectedLevel === \"secondary\") return [\"1\", \"2\", \"3\", \"4\"];\n    if (selectedLevel === \"advance\") return [\"5\", \"6\"];\n    return [];\n  }, [selectedLevel]);\n\n  // Available subjects based on level\n  const availableSubjects = useMemo(() => {\n    if (selectedLevel === \"primary\") return primarySubjects;\n    if (selectedLevel === \"secondary\") return secondarySubjects;\n    if (selectedLevel === \"advance\") return advanceSubjects;\n    return [];\n  }, [selectedLevel]);\n\n  // Fetch videos\n  const fetchVideos = useCallback(async () => {\n    try {\n      var _response$data;\n      setLoading(true);\n      setError(null);\n      dispatch(ShowLoading());\n      const filters = {\n        level: selectedLevel,\n        className: \"\",\n        // Get all classes for the level\n        subject: \"\",\n        // Get all subjects for the level\n        content: \"videos\"\n      };\n      const response = await getStudyMaterial(filters);\n      if (response !== null && response !== void 0 && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.success) {\n        const videoData = response.data.data || [];\n        console.log('📹 Fetched videos:', videoData);\n        console.log('📹 Sample video structure:', videoData[0]);\n        setVideos(videoData);\n      } else {\n        var _response$data2;\n        setError((response === null || response === void 0 ? void 0 : (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message) || \"Failed to fetch videos\");\n        setVideos([]);\n      }\n    } catch (error) {\n      console.error(\"Error fetching videos:\", error);\n      setError(\"Failed to load videos. Please try again.\");\n      setVideos([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [selectedLevel, dispatch]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    console.log('🔍 Filtering videos:', {\n      totalVideos: videos.length,\n      selectedLevel,\n      selectedClass,\n      selectedSubject,\n      searchTerm\n    });\n    let filtered = videos;\n\n    // Apply level filter\n    const levelFiltered = filtered.filter(video => {\n      const matches = video.level === selectedLevel;\n      if (!matches) {\n        console.log('❌ Level mismatch:', video.level, 'vs', selectedLevel);\n      }\n      return matches;\n    });\n    console.log('📊 After level filter:', levelFiltered.length);\n\n    // Apply class filter\n    if (selectedClass !== \"all\") {\n      const classFiltered = levelFiltered.filter(video => {\n        const videoClass = video.className || video.class;\n        const matches = videoClass === selectedClass;\n        if (!matches) {\n          console.log('❌ Class mismatch:', videoClass, 'vs', selectedClass);\n        }\n        return matches;\n      });\n      console.log('📊 After class filter:', classFiltered.length);\n      filtered = classFiltered;\n    } else {\n      filtered = levelFiltered;\n    }\n\n    // Apply subject filter\n    if (selectedSubject !== \"all\") {\n      filtered = filtered.filter(video => video.subject === selectedSubject);\n      console.log('📊 After subject filter:', filtered.length);\n    }\n\n    // Apply search filter\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(video => {\n        var _video$title, _video$subject, _video$topic;\n        return ((_video$title = video.title) === null || _video$title === void 0 ? void 0 : _video$title.toLowerCase().includes(searchLower)) || ((_video$subject = video.subject) === null || _video$subject === void 0 ? void 0 : _video$subject.toLowerCase().includes(searchLower)) || ((_video$topic = video.topic) === null || _video$topic === void 0 ? void 0 : _video$topic.toLowerCase().includes(searchLower));\n      });\n      console.log('📊 After search filter:', filtered.length);\n    }\n\n    // Apply sorting\n    const sorted = [...filtered].sort((a, b) => {\n      switch (sortBy) {\n        case \"newest\":\n          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);\n        case \"oldest\":\n          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);\n        case \"title\":\n          return (a.title || \"\").localeCompare(b.title || \"\");\n        case \"subject\":\n          return (a.subject || \"\").localeCompare(b.subject || \"\");\n        default:\n          return 0;\n      }\n    });\n    console.log('✅ Final filtered videos:', sorted.length);\n    return sorted;\n  }, [videos, searchTerm, sortBy, selectedLevel, selectedClass, selectedSubject]);\n\n  // Video handlers\n  const handleShowVideo = async index => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video !== null && video !== void 0 && video.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    if (videoRef) {\n      videoRef.pause();\n    }\n  };\n  const toggleVideoExpansion = () => {\n    setIsVideoExpanded(!isVideoExpanded);\n  };\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async videoUrl => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.error('❌ Error getting signed URL:', error);\n        return videoUrl;\n      }\n    }\n    return videoUrl;\n  };\n\n  // Get thumbnail URL\n  const getThumbnailUrl = video => {\n    if (video.thumbnail) {\n      return video.thumbnail;\n    }\n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      let videoId = video.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    return '/api/placeholder/400/225';\n  };\n\n  // Effects\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.level) {\n      setSelectedLevel(user.level);\n    }\n    if (user !== null && user !== void 0 && user.class) {\n      setSelectedClass(user.class);\n    }\n  }, [user]);\n\n  // Clear search and refresh\n  const handleClearSearch = () => {\n    setSearchTerm(\"\");\n  };\n  const handleRefresh = () => {\n    // Only refresh data, don't clear filters or search\n    fetchVideos();\n  };\n  const handleClearAll = () => {\n    setSearchTerm(\"\");\n    setSelectedSubject(\"all\");\n    setSelectedClass(\"all\");\n    fetchVideos();\n  };\n\n  // Comment functions\n  const handleAddComment = () => {\n    if (newComment.trim()) {\n      const comment = {\n        id: Date.now(),\n        text: newComment,\n        author: (user === null || user === void 0 ? void 0 : user.name) || \"Anonymous\",\n        timestamp: new Date().toISOString(),\n        replies: []\n      };\n      setComments([...comments, comment]);\n      setNewComment(\"\");\n    }\n  };\n  const handleAddReply = commentId => {\n    if (replyText.trim()) {\n      const reply = {\n        id: Date.now(),\n        text: replyText,\n        author: (user === null || user === void 0 ? void 0 : user.name) || \"Anonymous\",\n        timestamp: new Date().toISOString()\n      };\n      setComments(comments.map(comment => comment.id === commentId ? {\n        ...comment,\n        replies: [...comment.replies, reply]\n      } : comment));\n      setReplyText(\"\");\n      setReplyingTo(null);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-lessons-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-main\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-icon\",\n            children: /*#__PURE__*/_jsxDEV(TbVideo, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              children: \"Video Lessons\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Watch educational videos to enhance your learning\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"level-display\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"current-level\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"level-label\",\n              children: \"Level:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"level-value\",\n              children: selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"current-class\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"class-label\",\n              children: \"Your Class:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"class-value\",\n              children: (user === null || user === void 0 ? void 0 : user.level) === 'primary' ? `Class ${(user === null || user === void 0 ? void 0 : user.class) || 'N/A'}` : (user === null || user === void 0 ? void 0 : user.level) === 'secondary' ? `Form ${(user === null || user === void 0 ? void 0 : user.class) || 'N/A'}` : (user === null || user === void 0 ? void 0 : user.level) === 'advance' ? `Form ${(user === null || user === void 0 ? void 0 : user.class) || 'N/A'}` : 'Not Set'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"controls-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbSchool, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 17\n              }, this), \"Level\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedLevel,\n              onChange: e => setSelectedLevel(e.target.value),\n              className: \"control-select level-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"primary\",\n                children: \"Primary (1-7)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"secondary\",\n                children: \"Secondary (1-4)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"advance\",\n                children: \"Advance (5-6)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbFilter, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 17\n              }, this), \"Filter by Class\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedClass,\n              onChange: e => setSelectedClass(e.target.value),\n              className: \"control-select class-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Classes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 17\n              }, this), availableClasses.map(cls => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: cls,\n                children: selectedLevel === 'primary' ? `Class ${cls}` : `Form ${cls}`\n              }, cls, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbFilter, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this), \"Subject\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedSubject,\n              onChange: e => setSelectedSubject(e.target.value),\n              className: \"control-select subject-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Subjects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 17\n              }, this), availableSubjects.map(subject => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: subject,\n                children: subject\n              }, subject, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbSortAscending, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 17\n              }, this), \"Sort\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: sortBy,\n              onChange: e => setSortBy(e.target.value),\n              className: \"control-select sort-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"newest\",\n                children: \"Newest First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"oldest\",\n                children: \"Oldest First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"title\",\n                children: \"Title A-Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"subject\",\n                children: \"Subject A-Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-container\",\n            children: [/*#__PURE__*/_jsxDEV(TbSearch, {\n              className: \"search-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search videos by title, subject, or topic...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              className: \"search-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleClearSearch,\n              className: \"clear-search-btn\",\n              children: [/*#__PURE__*/_jsxDEV(TbX, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 19\n              }, this), \"Clear Search\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRefresh,\n            className: \"refresh-btn\",\n            children: [/*#__PURE__*/_jsxDEV(TbDownload, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this), \"Refresh All\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading videos...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-state\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n          className: \"error-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Error Loading Videos\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchVideos,\n          className: \"retry-btn\",\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 493,\n        columnNumber: 11\n      }, this) : filteredAndSortedVideos.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"videos-grid\",\n        children: filteredAndSortedVideos.map((video, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"video-card\",\n          onClick: () => handleShowVideo(index),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-card-thumbnail\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: getThumbnailUrl(video),\n              alt: video.title,\n              className: \"thumbnail-image\",\n              onError: e => {\n                // Fallback logic for failed thumbnails\n                if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                  // For YouTube videos, try different quality thumbnails\n                  let videoId = video.videoID;\n                  if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                    const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                    videoId = match ? match[1] : videoId;\n                  }\n                  const fallbacks = [`https://img.youtube.com/vi/${videoId}/hqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/default.jpg`, '/api/placeholder/320/180'];\n                  const currentSrc = e.target.src;\n                  const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                  if (currentIndex < fallbacks.length - 1) {\n                    e.target.src = fallbacks[currentIndex + 1];\n                  }\n                } else {\n                  e.target.src = '/api/placeholder/320/180';\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"play-overlay\",\n              children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                className: \"play-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-duration\",\n              children: video.duration || \"Video\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 19\n            }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"subtitle-badge\",\n              children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 23\n              }, this), \"CC\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-card-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"video-title\",\n              children: video.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"video-subject\",\n                children: video.subject\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"video-class\",\n                children: selectedLevel === 'primary' ? `Class ${video.className || video.class}` : `Form ${video.className || video.class}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-tags\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"level-tag\",\n                children: video.level\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 21\n              }, this), video.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"topic-tag\",\n                children: video.topic\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 37\n              }, this), video.sharedFromClass && video.sharedFromClass !== video.className && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"shared-tag\",\n                children: [\"Shared from \", selectedLevel === 'primary' ? `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 502,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n          className: \"empty-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No Videos Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No video lessons are available for your current selection.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 577,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"suggestion\",\n          children: \"Try selecting a different class or subject.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 380,\n      columnNumber: 7\n    }, this), showVideoIndices.length > 0 && currentVideoIndex !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `video-overlay ${isVideoExpanded ? 'expanded' : ''}`,\n      onClick: e => {\n        if (e.target === e.currentTarget) handleHideVideo();\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `video-modal ${isVideoExpanded ? 'expanded' : ''}`,\n        children: (() => {\n          const video = filteredAndSortedVideos[currentVideoIndex];\n          if (!video) return /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Video not found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 34\n          }, this);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"video-title\",\n                  children: video.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-subject\",\n                    children: video.subject\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 599,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-class\",\n                    children: [\"Class \", video.className]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 600,\n                    columnNumber: 25\n                  }, this), video.level && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-level\",\n                    children: video.level\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 598,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-controls\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"control-btn expand-btn\",\n                  onClick: toggleVideoExpansion,\n                  title: isVideoExpanded ? \"Exit fullscreen\" : \"Enter fullscreen\",\n                  children: isVideoExpanded ? /*#__PURE__*/_jsxDEV(FaCompress, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 44\n                  }, this) : /*#__PURE__*/_jsxDEV(FaExpand, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 61\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 605,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"control-btn close-btn\",\n                  onClick: handleHideVideo,\n                  title: \"Close video\",\n                  children: /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 617,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 612,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-container\",\n              children: video.videoUrl ? /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '15px',\n                  background: '#000',\n                  borderRadius: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"video\", {\n                  ref: ref => setVideoRef(ref),\n                  controls: true,\n                  autoPlay: true,\n                  playsInline: true,\n                  preload: \"metadata\",\n                  width: \"100%\",\n                  height: \"400\",\n                  poster: getThumbnailUrl(video),\n                  style: {\n                    width: '100%',\n                    height: '400px',\n                    backgroundColor: '#000'\n                  },\n                  onError: e => {\n                    setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                  },\n                  onCanPlay: () => {\n                    setVideoError(null);\n                  },\n                  onLoadStart: () => {\n                    console.log('🎬 Video loading started');\n                  },\n                  crossOrigin: \"anonymous\",\n                  children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                    src: video.signedVideoUrl || video.videoUrl,\n                    type: \"video/mp4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 650,\n                    columnNumber: 29\n                  }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => /*#__PURE__*/_jsxDEV(\"track\", {\n                    kind: \"subtitles\",\n                    src: subtitle.url,\n                    srcLang: subtitle.language,\n                    label: subtitle.languageName,\n                    default: subtitle.isDefault || index === 0\n                  }, `${subtitle.language}-${index}`, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 654,\n                    columnNumber: 31\n                  }, this)), \"Your browser does not support the video tag.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 624,\n                  columnNumber: 27\n                }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"subtitle-indicator\",\n                  children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {\n                    className: \"subtitle-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 670,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Subtitles available in \", video.subtitles.length, \" language(s)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 671,\n                    columnNumber: 31\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 669,\n                  columnNumber: 29\n                }, this), videoError && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-error-overlay\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"error-content\",\n                    children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n                      className: \"error-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 679,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: videoError\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 680,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setVideoError(null),\n                      className: \"dismiss-error-btn\",\n                      children: \"Dismiss\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 681,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 678,\n                    columnNumber: 31\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 29\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 23\n              }, this) : video.videoID ?\n              /*#__PURE__*/\n              // Fallback to YouTube embed if no videoUrl\n              _jsxDEV(\"iframe\", {\n                src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                title: video.title,\n                frameBorder: \"0\",\n                allowFullScreen: true,\n                className: \"video-iframe\",\n                onLoad: () => console.log('✅ YouTube iframe loaded')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 690,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-error\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"error-icon\",\n                  children: \"\\u26A0\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"Video Unavailable\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 701,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: videoError || \"This video cannot be played at the moment.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 702,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"error-actions\",\n                  children: /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: video.signedVideoUrl || video.videoUrl,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: \"external-link-btn\",\n                    children: \"\\uD83D\\uDCF1 Open in New Tab\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 704,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 703,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 19\n            }, this), !isVideoExpanded && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"comments-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"comments-title\",\n                children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 721,\n                  columnNumber: 25\n                }, this), \"Discussion (\", comments.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"add-comment\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"comment-input-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                    value: newComment,\n                    onChange: e => setNewComment(e.target.value),\n                    placeholder: \"Share your thoughts about this video...\",\n                    className: \"comment-input\",\n                    rows: \"3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 728,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: handleAddComment,\n                    className: \"comment-submit-btn\",\n                    disabled: !newComment.trim(),\n                    children: \"Post Comment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 735,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"comments-list\",\n                children: comments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"no-comments\",\n                  children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 749,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"No comments yet. Be the first to share your thoughts!\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 750,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 748,\n                  columnNumber: 27\n                }, this) : comments.map(comment => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"comment\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"comment-header\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"comment-author\",\n                      children: comment.author\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 756,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"comment-time\",\n                      children: new Date(comment.timestamp).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 757,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 755,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"comment-text\",\n                    children: comment.text\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 761,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"comment-actions\",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setReplyingTo(comment.id),\n                      className: \"reply-btn\",\n                      children: \"Reply\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 763,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 762,\n                    columnNumber: 31\n                  }, this), replyingTo === comment.id && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"reply-input-container\",\n                    children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                      value: replyText,\n                      onChange: e => setReplyText(e.target.value),\n                      placeholder: \"Write a reply...\",\n                      className: \"reply-input\",\n                      rows: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 774,\n                      columnNumber: 35\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"reply-actions\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleAddReply(comment.id),\n                        className: \"reply-submit-btn\",\n                        disabled: !replyText.trim(),\n                        children: \"Reply\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 782,\n                        columnNumber: 37\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setReplyingTo(null);\n                          setReplyText(\"\");\n                        },\n                        className: \"reply-cancel-btn\",\n                        children: \"Cancel\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 789,\n                        columnNumber: 37\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 781,\n                      columnNumber: 35\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 773,\n                    columnNumber: 33\n                  }, this), comment.replies.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"replies\",\n                    children: comment.replies.map(reply => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"reply\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"reply-header\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"reply-author\",\n                          children: reply.author\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 808,\n                          columnNumber: 41\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"reply-time\",\n                          children: new Date(reply.timestamp).toLocaleDateString()\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 809,\n                          columnNumber: 41\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 807,\n                        columnNumber: 39\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"reply-text\",\n                        children: reply.text\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 813,\n                        columnNumber: 39\n                      }, this)]\n                    }, reply.id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 806,\n                      columnNumber: 37\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 804,\n                    columnNumber: 33\n                  }, this)]\n                }, comment.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 754,\n                  columnNumber: 29\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 746,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 17\n          }, this);\n        })()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 588,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 347,\n    columnNumber: 5\n  }, this);\n}\n_s(VideoLessons, \"2RM1C8y+aNUMRZ203dp0PcHuV+c=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = VideoLessons;\nexport default VideoLessons;\nvar _c;\n$RefreshReg$(_c, \"VideoLessons\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "motion", "AnimatePresence", "getStudyMaterial", "useDispatch", "useSelector", "HideLoading", "ShowLoading", "FaPlayCircle", "FaVideo", "FaGraduationCap", "FaDownload", "FaEye", "FaTimes", "FaChevronDown", "FaSearch", "FaExpand", "FaCompress", "TbVideo", "TbSchool", "TbSearch", "Tb<PERSON><PERSON>er", "TbSortAscending", "TbDownload", "TbEye", "TbCalendar", "TbUser", "TbChevronDown", "TbChevronDownIcon", "TbChevronUp", "TbX", "TbAlertTriangle", "TbInfoCircle", "TbCheck", "primarySubjects", "secondarySubjects", "advanceSubjects", "jsxDEV", "_jsxDEV", "VideoLessons", "_s", "user", "state", "dispatch", "videos", "setVideos", "loading", "setLoading", "error", "setError", "selectedLevel", "setSelectedLevel", "level", "selectedClass", "setSelectedClass", "selectedSubject", "setSelectedSubject", "searchTerm", "setSearchTerm", "sortBy", "setSortBy", "currentVideoIndex", "setCurrentVideoIndex", "showVideoIndices", "setShowVideoIndices", "isVideoExpanded", "setIsVideoExpanded", "videoError", "setVideoError", "videoRef", "setVideoRef", "comments", "setComments", "newComment", "setNewComment", "replyingTo", "setReplyingTo", "replyText", "setReplyText", "availableClasses", "availableSubjects", "fetchVideos", "_response$data", "filters", "className", "subject", "content", "response", "data", "success", "videoData", "console", "log", "_response$data2", "message", "filteredAndSortedVideos", "totalVideos", "length", "filtered", "levelFiltered", "filter", "video", "matches", "classFiltered", "videoClass", "class", "trim", "searchLower", "toLowerCase", "_video$title", "_video$subject", "_video$topic", "title", "includes", "topic", "sorted", "sort", "a", "b", "Date", "createdAt", "localeCompare", "handleShowVideo", "index", "videoUrl", "signedUrl", "getSignedVideoUrl", "signedVideoUrl", "warn", "handleHideVideo", "pause", "toggleVideoExpansion", "fetch", "encodeURIComponent", "method", "headers", "ok", "Error", "status", "json", "getThumbnailUrl", "thumbnail", "videoID", "videoId", "match", "handleClearSearch", "handleRefresh", "handleClearAll", "handleAddComment", "comment", "id", "now", "text", "author", "name", "timestamp", "toISOString", "replies", "handleAddReply", "commentId", "reply", "map", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "char<PERSON>t", "toUpperCase", "slice", "value", "onChange", "e", "target", "cls", "type", "placeholder", "onClick", "src", "alt", "onError", "fallbacks", "currentSrc", "currentIndex", "findIndex", "url", "split", "pop", "duration", "subtitles", "sharedFromClass", "currentTarget", "style", "padding", "background", "borderRadius", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "poster", "backgroundColor", "onCanPlay", "onLoadStart", "crossOrigin", "subtitle", "kind", "srcLang", "language", "label", "languageName", "default", "isDefault", "frameBorder", "allowFullScreen", "onLoad", "href", "rel", "rows", "disabled", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/index.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\n\nimport {\n  FaPlayCircle,\n  FaVideo,\n  FaGraduationCap,\n  FaDownload,\n  FaEye,\n  FaTimes,\n  FaChevronDown,\n  FaSearch,\n  FaExpand,\n  FaCompress,\n} from \"react-icons/fa\";\nimport {\n  TbVideo,\n  TbSchool,\n  TbSearch,\n  TbFilter,\n  TbSortAscending,\n  TbDownload,\n  TbEye,\n  TbCalendar,\n  TbUser,\n  TbChevronDown as TbChevronDownIcon,\n  TbChevronUp,\n  TbX,\n  Tb<PERSON>lert<PERSON>riangle,\n  TbIn<PERSON><PERSON><PERSON><PERSON>,\n  Tb<PERSON><PERSON><PERSON>,\n} from \"react-icons/tb\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\n\nfunction VideoLessons() {\n  const { user } = useSelector((state) => state.user);\n  const dispatch = useDispatch();\n\n  // State management\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedLevel, setSelectedLevel] = useState(user?.level || \"primary\");\n  const [selectedClass, setSelectedClass] = useState(\"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Video player state\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [videoError, setVideoError] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n\n  // Comments state\n  const [comments, setComments] = useState([]);\n  const [newComment, setNewComment] = useState(\"\");\n  const [replyingTo, setReplyingTo] = useState(null);\n  const [replyText, setReplyText] = useState(\"\");\n\n  // Available classes based on level\n  const availableClasses = useMemo(() => {\n    if (selectedLevel === \"primary\") return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    if (selectedLevel === \"secondary\") return [\"1\", \"2\", \"3\", \"4\"];\n    if (selectedLevel === \"advance\") return [\"5\", \"6\"];\n    return [];\n  }, [selectedLevel]);\n\n  // Available subjects based on level\n  const availableSubjects = useMemo(() => {\n    if (selectedLevel === \"primary\") return primarySubjects;\n    if (selectedLevel === \"secondary\") return secondarySubjects;\n    if (selectedLevel === \"advance\") return advanceSubjects;\n    return [];\n  }, [selectedLevel]);\n\n  // Fetch videos\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      dispatch(ShowLoading());\n\n      const filters = {\n        level: selectedLevel,\n        className: \"\", // Get all classes for the level\n        subject: \"\", // Get all subjects for the level\n        content: \"videos\"\n      };\n\n      const response = await getStudyMaterial(filters);\n\n      if (response?.data?.success) {\n        const videoData = response.data.data || [];\n        console.log('📹 Fetched videos:', videoData);\n        console.log('📹 Sample video structure:', videoData[0]);\n        setVideos(videoData);\n      } else {\n        setError(response?.data?.message || \"Failed to fetch videos\");\n        setVideos([]);\n      }\n    } catch (error) {\n      console.error(\"Error fetching videos:\", error);\n      setError(\"Failed to load videos. Please try again.\");\n      setVideos([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [selectedLevel, dispatch]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    console.log('🔍 Filtering videos:', {\n      totalVideos: videos.length,\n      selectedLevel,\n      selectedClass,\n      selectedSubject,\n      searchTerm\n    });\n\n    let filtered = videos;\n\n    // Apply level filter\n    const levelFiltered = filtered.filter(video => {\n      const matches = video.level === selectedLevel;\n      if (!matches) {\n        console.log('❌ Level mismatch:', video.level, 'vs', selectedLevel);\n      }\n      return matches;\n    });\n    console.log('📊 After level filter:', levelFiltered.length);\n\n    // Apply class filter\n    if (selectedClass !== \"all\") {\n      const classFiltered = levelFiltered.filter(video => {\n        const videoClass = video.className || video.class;\n        const matches = videoClass === selectedClass;\n        if (!matches) {\n          console.log('❌ Class mismatch:', videoClass, 'vs', selectedClass);\n        }\n        return matches;\n      });\n      console.log('📊 After class filter:', classFiltered.length);\n      filtered = classFiltered;\n    } else {\n      filtered = levelFiltered;\n    }\n\n    // Apply subject filter\n    if (selectedSubject !== \"all\") {\n      filtered = filtered.filter(video => video.subject === selectedSubject);\n      console.log('📊 After subject filter:', filtered.length);\n    }\n\n    // Apply search filter\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(video =>\n        video.title?.toLowerCase().includes(searchLower) ||\n        video.subject?.toLowerCase().includes(searchLower) ||\n        video.topic?.toLowerCase().includes(searchLower)\n      );\n      console.log('📊 After search filter:', filtered.length);\n    }\n\n    // Apply sorting\n    const sorted = [...filtered].sort((a, b) => {\n      switch (sortBy) {\n        case \"newest\":\n          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);\n        case \"oldest\":\n          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);\n        case \"title\":\n          return (a.title || \"\").localeCompare(b.title || \"\");\n        case \"subject\":\n          return (a.subject || \"\").localeCompare(b.subject || \"\");\n        default:\n          return 0;\n      }\n    });\n\n    console.log('✅ Final filtered videos:', sorted.length);\n    return sorted;\n  }, [videos, searchTerm, sortBy, selectedLevel, selectedClass, selectedSubject]);\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedVideos[index];\n\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    if (videoRef) {\n      videoRef.pause();\n    }\n  };\n\n  const toggleVideoExpansion = () => {\n    setIsVideoExpanded(!isVideoExpanded);\n  };\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async (videoUrl) => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n          }\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.error('❌ Error getting signed URL:', error);\n        return videoUrl;\n      }\n    }\n\n    return videoUrl;\n  };\n\n  // Get thumbnail URL\n  const getThumbnailUrl = (video) => {\n    if (video.thumbnail) {\n      return video.thumbnail;\n    }\n    \n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      let videoId = video.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    \n    return '/api/placeholder/400/225';\n  };\n\n  // Effects\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  useEffect(() => {\n    if (user?.level) {\n      setSelectedLevel(user.level);\n    }\n    if (user?.class) {\n      setSelectedClass(user.class);\n    }\n  }, [user]);\n\n  // Clear search and refresh\n  const handleClearSearch = () => {\n    setSearchTerm(\"\");\n  };\n\n  const handleRefresh = () => {\n    // Only refresh data, don't clear filters or search\n    fetchVideos();\n  };\n\n  const handleClearAll = () => {\n    setSearchTerm(\"\");\n    setSelectedSubject(\"all\");\n    setSelectedClass(\"all\");\n    fetchVideos();\n  };\n\n  // Comment functions\n  const handleAddComment = () => {\n    if (newComment.trim()) {\n      const comment = {\n        id: Date.now(),\n        text: newComment,\n        author: user?.name || \"Anonymous\",\n        timestamp: new Date().toISOString(),\n        replies: []\n      };\n      setComments([...comments, comment]);\n      setNewComment(\"\");\n    }\n  };\n\n  const handleAddReply = (commentId) => {\n    if (replyText.trim()) {\n      const reply = {\n        id: Date.now(),\n        text: replyText,\n        author: user?.name || \"Anonymous\",\n        timestamp: new Date().toISOString()\n      };\n\n      setComments(comments.map(comment =>\n        comment.id === commentId\n          ? { ...comment, replies: [...comment.replies, reply] }\n          : comment\n      ));\n      setReplyText(\"\");\n      setReplyingTo(null);\n    }\n  };\n\n  return (\n    <div className=\"video-lessons-container\">\n      {/* Enhanced Header with Level Display */}\n      <div className=\"video-lessons-header\">\n        <div className=\"header-content\">\n          <div className=\"header-main\">\n            <div className=\"header-icon\">\n              <TbVideo />\n            </div>\n            <div className=\"header-text\">\n              <h1>Video Lessons</h1>\n              <p>Watch educational videos to enhance your learning</p>\n            </div>\n          </div>\n\n          {/* Level and Class Display */}\n          <div className=\"level-display\">\n            <div className=\"current-level\">\n              <span className=\"level-label\">Level:</span>\n              <span className=\"level-value\">{selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)}</span>\n            </div>\n            <div className=\"current-class\">\n              <span className=\"class-label\">Your Class:</span>\n              <span className=\"class-value\">\n                {user?.level === 'primary' ? `Class ${user?.class || 'N/A'}` :\n                 user?.level === 'secondary' ? `Form ${user?.class || 'N/A'}` :\n                 user?.level === 'advance' ? `Form ${user?.class || 'N/A'}` :\n                 'Not Set'}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"video-lessons-content\">\n        {/* Enhanced Filters and Controls */}\n        <div className=\"video-controls\">\n          <div className=\"controls-row\">\n            {/* Level Selector */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbSchool />\n                Level\n              </label>\n              <select\n                value={selectedLevel}\n                onChange={(e) => setSelectedLevel(e.target.value)}\n                className=\"control-select level-select\"\n              >\n                <option value=\"primary\">Primary (1-7)</option>\n                <option value=\"secondary\">Secondary (1-4)</option>\n                <option value=\"advance\">Advance (5-6)</option>\n              </select>\n            </div>\n\n            {/* Class Filter */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbFilter />\n                Filter by Class\n              </label>\n              <select\n                value={selectedClass}\n                onChange={(e) => setSelectedClass(e.target.value)}\n                className=\"control-select class-select\"\n              >\n                <option value=\"all\">All Classes</option>\n                {availableClasses.map((cls) => (\n                  <option key={cls} value={cls}>\n                    {selectedLevel === 'primary' ? `Class ${cls}` : `Form ${cls}`}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Subject Filter */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbFilter />\n                Subject\n              </label>\n              <select\n                value={selectedSubject}\n                onChange={(e) => setSelectedSubject(e.target.value)}\n                className=\"control-select subject-select\"\n              >\n                <option value=\"all\">All Subjects</option>\n                {availableSubjects.map((subject) => (\n                  <option key={subject} value={subject}>\n                    {subject}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Sort */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbSortAscending />\n                Sort\n              </label>\n              <select\n                value={sortBy}\n                onChange={(e) => setSortBy(e.target.value)}\n                className=\"control-select sort-select\"\n              >\n                <option value=\"newest\">Newest First</option>\n                <option value=\"oldest\">Oldest First</option>\n                <option value=\"title\">Title A-Z</option>\n                <option value=\"subject\">Subject A-Z</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Search Row */}\n          <div className=\"search-row\">\n            <div className=\"search-container\">\n              <TbSearch className=\"search-icon\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search videos by title, subject, or topic...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"search-input\"\n              />\n              {searchTerm && (\n                <button onClick={handleClearSearch} className=\"clear-search-btn\">\n                  <TbX />\n                  Clear Search\n                </button>\n              )}\n            </div>\n\n            <button onClick={handleRefresh} className=\"refresh-btn\">\n              <TbDownload />\n              Refresh All\n            </button>\n          </div>\n        </div>\n\n        {/* Loading State */}\n        {loading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>Loading videos...</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <TbAlertTriangle className=\"error-icon\" />\n            <h3>Error Loading Videos</h3>\n            <p>{error}</p>\n            <button onClick={fetchVideos} className=\"retry-btn\">\n              Try Again\n            </button>\n          </div>\n        ) : filteredAndSortedVideos.length > 0 ? (\n          <div className=\"videos-grid\">\n            {filteredAndSortedVideos.map((video, index) => (\n              <div key={index} className=\"video-card\" onClick={() => handleShowVideo(index)}>\n                <div className=\"video-card-thumbnail\">\n                  <img\n                    src={getThumbnailUrl(video)}\n                    alt={video.title}\n                    className=\"thumbnail-image\"\n                    onError={(e) => {\n                      // Fallback logic for failed thumbnails\n                      if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                        // For YouTube videos, try different quality thumbnails\n                        let videoId = video.videoID;\n                        if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                          const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                          videoId = match ? match[1] : videoId;\n                        }\n\n                        const fallbacks = [\n                          `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n                          `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,\n                          `https://img.youtube.com/vi/${videoId}/default.jpg`,\n                          '/api/placeholder/320/180'\n                        ];\n\n                        const currentSrc = e.target.src;\n                        const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n\n                        if (currentIndex < fallbacks.length - 1) {\n                          e.target.src = fallbacks[currentIndex + 1];\n                        }\n                      } else {\n                        e.target.src = '/api/placeholder/320/180';\n                      }\n                    }}\n                  />\n                  <div className=\"play-overlay\">\n                    <FaPlayCircle className=\"play-icon\" />\n                  </div>\n                  <div className=\"video-duration\">\n                    {video.duration || \"Video\"}\n                  </div>\n                  {video.subtitles && video.subtitles.length > 0 && (\n                    <div className=\"subtitle-badge\">\n                      <TbInfoCircle />\n                      CC\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"video-card-content\">\n                  <h3 className=\"video-title\">{video.title}</h3>\n                  <div className=\"video-meta\">\n                    <span className=\"video-subject\">{video.subject}</span>\n                    <span className=\"video-class\">\n                      {selectedLevel === 'primary' ? `Class ${video.className || video.class}` : `Form ${video.className || video.class}`}\n                    </span>\n                  </div>\n                  <div className=\"video-tags\">\n                    <span className=\"level-tag\">{video.level}</span>\n                    {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n                    {video.sharedFromClass && video.sharedFromClass !== video.className && (\n                      <span className=\"shared-tag\">\n                        Shared from {selectedLevel === 'primary' ? `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`}\n                      </span>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>No Videos Found</h3>\n            <p>No video lessons are available for your current selection.</p>\n            <p className=\"suggestion\">Try selecting a different class or subject.</p>\n          </div>\n        )}\n      </div>\n\n      {/* Enhanced Video Display */}\n      {showVideoIndices.length > 0 && currentVideoIndex !== null && (\n        <div className={`video-overlay ${isVideoExpanded ? 'expanded' : ''}`} onClick={(e) => {\n          if (e.target === e.currentTarget) handleHideVideo();\n        }}>\n          <div className={`video-modal ${isVideoExpanded ? 'expanded' : ''}`}>\n            {(() => {\n              const video = filteredAndSortedVideos[currentVideoIndex];\n              if (!video) return <div>Video not found</div>;\n\n              return (\n                <div className=\"video-content\">\n                  <div className=\"video-header\">\n                    <div className=\"video-info\">\n                      <h3 className=\"video-title\">{video.title}</h3>\n                      <div className=\"video-meta\">\n                        <span className=\"video-subject\">{video.subject}</span>\n                        <span className=\"video-class\">Class {video.className}</span>\n                        {video.level && <span className=\"video-level\">{video.level}</span>}\n                      </div>\n                    </div>\n                    <div className=\"video-controls\">\n                      <button\n                        className=\"control-btn expand-btn\"\n                        onClick={toggleVideoExpansion}\n                        title={isVideoExpanded ? \"Exit fullscreen\" : \"Enter fullscreen\"}\n                      >\n                        {isVideoExpanded ? <FaCompress /> : <FaExpand />}\n                      </button>\n                      <button\n                        className=\"control-btn close-btn\"\n                        onClick={handleHideVideo}\n                        title=\"Close video\"\n                      >\n                        <FaTimes />\n                      </button>\n                    </div>\n                  </div>\n                  <div className=\"video-container\">\n                    {video.videoUrl ? (\n                      <div style={{ padding: '15px', background: '#000', borderRadius: '8px' }}>\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"400\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '400px',\n                              backgroundColor: '#000'\n                            }}\n                            onError={(e) => {\n                              setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                            }}\n                            onCanPlay={() => {\n                              setVideoError(null);\n                            }}\n                            onLoadStart={() => {\n                              console.log('🎬 Video loading started');\n                            }}\n                            crossOrigin=\"anonymous\"\n                          >\n                            {/* Use signed URL if available, otherwise use original URL */}\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n\n                            {/* Add subtitle tracks if available */}\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n\n                            Your browser does not support the video tag.\n                          </video>\n\n                          {/* Subtitle indicator */}\n                          {video.subtitles && video.subtitles.length > 0 && (\n                            <div className=\"subtitle-indicator\">\n                              <TbInfoCircle className=\"subtitle-icon\" />\n                              <span>Subtitles available in {video.subtitles.length} language(s)</span>\n                            </div>\n                          )}\n\n                          {/* Video error display */}\n                          {videoError && (\n                            <div className=\"video-error-overlay\">\n                              <div className=\"error-content\">\n                                <TbAlertTriangle className=\"error-icon\" />\n                                <p>{videoError}</p>\n                                <button onClick={() => setVideoError(null)} className=\"dismiss-error-btn\">\n                                  Dismiss\n                                </button>\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                    ) : video.videoID ? (\n                      // Fallback to YouTube embed if no videoUrl\n                      <iframe\n                        src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                        title={video.title}\n                        frameBorder=\"0\"\n                        allowFullScreen\n                        className=\"video-iframe\"\n                        onLoad={() => console.log('✅ YouTube iframe loaded')}\n                      ></iframe>\n                    ) : (\n                      <div className=\"video-error\">\n                        <div className=\"error-icon\">⚠️</div>\n                        <h3>Video Unavailable</h3>\n                        <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                        <div className=\"error-actions\">\n                          <a\n                            href={video.signedVideoUrl || video.videoUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"external-link-btn\"\n                          >\n                            📱 Open in New Tab\n                          </a>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Comments Section */}\n                  {!isVideoExpanded && (\n                    <div className=\"comments-section\">\n                      <h4 className=\"comments-title\">\n                        <TbInfoCircle />\n                        Discussion ({comments.length})\n                      </h4>\n\n                      {/* Add Comment */}\n                      <div className=\"add-comment\">\n                        <div className=\"comment-input-container\">\n                          <textarea\n                            value={newComment}\n                            onChange={(e) => setNewComment(e.target.value)}\n                            placeholder=\"Share your thoughts about this video...\"\n                            className=\"comment-input\"\n                            rows=\"3\"\n                          />\n                          <button\n                            onClick={handleAddComment}\n                            className=\"comment-submit-btn\"\n                            disabled={!newComment.trim()}\n                          >\n                            Post Comment\n                          </button>\n                        </div>\n                      </div>\n\n                      {/* Comments List */}\n                      <div className=\"comments-list\">\n                        {comments.length === 0 ? (\n                          <div className=\"no-comments\">\n                            <TbInfoCircle />\n                            <p>No comments yet. Be the first to share your thoughts!</p>\n                          </div>\n                        ) : (\n                          comments.map((comment) => (\n                            <div key={comment.id} className=\"comment\">\n                              <div className=\"comment-header\">\n                                <span className=\"comment-author\">{comment.author}</span>\n                                <span className=\"comment-time\">\n                                  {new Date(comment.timestamp).toLocaleDateString()}\n                                </span>\n                              </div>\n                              <div className=\"comment-text\">{comment.text}</div>\n                              <div className=\"comment-actions\">\n                                <button\n                                  onClick={() => setReplyingTo(comment.id)}\n                                  className=\"reply-btn\"\n                                >\n                                  Reply\n                                </button>\n                              </div>\n\n                              {/* Reply Input */}\n                              {replyingTo === comment.id && (\n                                <div className=\"reply-input-container\">\n                                  <textarea\n                                    value={replyText}\n                                    onChange={(e) => setReplyText(e.target.value)}\n                                    placeholder=\"Write a reply...\"\n                                    className=\"reply-input\"\n                                    rows=\"2\"\n                                  />\n                                  <div className=\"reply-actions\">\n                                    <button\n                                      onClick={() => handleAddReply(comment.id)}\n                                      className=\"reply-submit-btn\"\n                                      disabled={!replyText.trim()}\n                                    >\n                                      Reply\n                                    </button>\n                                    <button\n                                      onClick={() => {\n                                        setReplyingTo(null);\n                                        setReplyText(\"\");\n                                      }}\n                                      className=\"reply-cancel-btn\"\n                                    >\n                                      Cancel\n                                    </button>\n                                  </div>\n                                </div>\n                              )}\n\n                              {/* Replies */}\n                              {comment.replies.length > 0 && (\n                                <div className=\"replies\">\n                                  {comment.replies.map((reply) => (\n                                    <div key={reply.id} className=\"reply\">\n                                      <div className=\"reply-header\">\n                                        <span className=\"reply-author\">{reply.author}</span>\n                                        <span className=\"reply-time\">\n                                          {new Date(reply.timestamp).toLocaleDateString()}\n                                        </span>\n                                      </div>\n                                      <div className=\"reply-text\">{reply.text}</div>\n                                    </div>\n                                  ))}\n                                </div>\n                              )}\n                            </div>\n                          ))\n                        )}\n                      </div>\n                    </div>\n                  )}\n                </div>\n              );\n            })()}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default VideoLessons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,OAAO,aAAa;AACpB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AAErE,SACEC,YAAY,EACZC,OAAO,EACPC,eAAe,EACfC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,aAAa,EACbC,QAAQ,EACRC,QAAQ,EACRC,UAAU,QACL,gBAAgB;AACvB,SACEC,OAAO,EACPC,QAAQ,EACRC,QAAQ,EACRC,QAAQ,EACRC,eAAe,EACfC,UAAU,EACVC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,aAAa,IAAIC,iBAAiB,EAClCC,WAAW,EACXC,GAAG,EACHC,eAAe,EACfC,YAAY,EACZC,OAAO,QACF,gBAAgB;AACvB,SAASC,eAAe,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjG,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGpC,WAAW,CAAEqC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAME,QAAQ,GAAGvC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACwC,MAAM,EAAEC,SAAS,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmD,KAAK,EAAEC,QAAQ,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqD,aAAa,EAAEC,gBAAgB,CAAC,GAAGtD,QAAQ,CAAC,CAAA4C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,KAAK,KAAI,SAAS,CAAC;EAC5E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC0D,eAAe,EAAEC,kBAAkB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4D,UAAU,EAAEC,aAAa,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8D,MAAM,EAAEC,SAAS,CAAC,GAAG/D,QAAQ,CAAC,QAAQ,CAAC;;EAE9C;EACA,MAAM,CAACgE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACkE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACoE,eAAe,EAAEC,kBAAkB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACwE,QAAQ,EAAEC,WAAW,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;;EAE9C;EACA,MAAM,CAAC0E,QAAQ,EAAEC,WAAW,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4E,UAAU,EAAEC,aAAa,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8E,UAAU,EAAEC,aAAa,CAAC,GAAG/E,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACgF,SAAS,EAAEC,YAAY,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAMkF,gBAAgB,GAAG/E,OAAO,CAAC,MAAM;IACrC,IAAIkD,aAAa,KAAK,SAAS,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC3E,IAAIA,aAAa,KAAK,WAAW,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC9D,IAAIA,aAAa,KAAK,SAAS,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IAClD,OAAO,EAAE;EACX,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAM8B,iBAAiB,GAAGhF,OAAO,CAAC,MAAM;IACtC,IAAIkD,aAAa,KAAK,SAAS,EAAE,OAAOhB,eAAe;IACvD,IAAIgB,aAAa,KAAK,WAAW,EAAE,OAAOf,iBAAiB;IAC3D,IAAIe,aAAa,KAAK,SAAS,EAAE,OAAOd,eAAe;IACvD,OAAO,EAAE;EACX,CAAC,EAAE,CAACc,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAM+B,WAAW,GAAGlF,WAAW,CAAC,YAAY;IAC1C,IAAI;MAAA,IAAAmF,cAAA;MACFnC,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACdN,QAAQ,CAACpC,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAM4E,OAAO,GAAG;QACd/B,KAAK,EAAEF,aAAa;QACpBkC,SAAS,EAAE,EAAE;QAAE;QACfC,OAAO,EAAE,EAAE;QAAE;QACbC,OAAO,EAAE;MACX,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAMpF,gBAAgB,CAACgF,OAAO,CAAC;MAEhD,IAAII,QAAQ,aAARA,QAAQ,gBAAAL,cAAA,GAARK,QAAQ,CAAEC,IAAI,cAAAN,cAAA,eAAdA,cAAA,CAAgBO,OAAO,EAAE;QAC3B,MAAMC,SAAS,GAAGH,QAAQ,CAACC,IAAI,CAACA,IAAI,IAAI,EAAE;QAC1CG,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,SAAS,CAAC;QAC5CC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEF,SAAS,CAAC,CAAC,CAAC,CAAC;QACvD7C,SAAS,CAAC6C,SAAS,CAAC;MACtB,CAAC,MAAM;QAAA,IAAAG,eAAA;QACL5C,QAAQ,CAAC,CAAAsC,QAAQ,aAARA,QAAQ,wBAAAM,eAAA,GAARN,QAAQ,CAAEC,IAAI,cAAAK,eAAA,uBAAdA,eAAA,CAAgBC,OAAO,KAAI,wBAAwB,CAAC;QAC7DjD,SAAS,CAAC,EAAE,CAAC;MACf;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd2C,OAAO,CAAC3C,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CC,QAAQ,CAAC,0CAA0C,CAAC;MACpDJ,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;MACjBJ,QAAQ,CAACrC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAAC4C,aAAa,EAAEP,QAAQ,CAAC,CAAC;;EAE7B;EACA,MAAMoD,uBAAuB,GAAG/F,OAAO,CAAC,MAAM;IAC5C2F,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;MAClCI,WAAW,EAAEpD,MAAM,CAACqD,MAAM;MAC1B/C,aAAa;MACbG,aAAa;MACbE,eAAe;MACfE;IACF,CAAC,CAAC;IAEF,IAAIyC,QAAQ,GAAGtD,MAAM;;IAErB;IACA,MAAMuD,aAAa,GAAGD,QAAQ,CAACE,MAAM,CAACC,KAAK,IAAI;MAC7C,MAAMC,OAAO,GAAGD,KAAK,CAACjD,KAAK,KAAKF,aAAa;MAC7C,IAAI,CAACoD,OAAO,EAAE;QACZX,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAES,KAAK,CAACjD,KAAK,EAAE,IAAI,EAAEF,aAAa,CAAC;MACpE;MACA,OAAOoD,OAAO;IAChB,CAAC,CAAC;IACFX,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEO,aAAa,CAACF,MAAM,CAAC;;IAE3D;IACA,IAAI5C,aAAa,KAAK,KAAK,EAAE;MAC3B,MAAMkD,aAAa,GAAGJ,aAAa,CAACC,MAAM,CAACC,KAAK,IAAI;QAClD,MAAMG,UAAU,GAAGH,KAAK,CAACjB,SAAS,IAAIiB,KAAK,CAACI,KAAK;QACjD,MAAMH,OAAO,GAAGE,UAAU,KAAKnD,aAAa;QAC5C,IAAI,CAACiD,OAAO,EAAE;UACZX,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEY,UAAU,EAAE,IAAI,EAAEnD,aAAa,CAAC;QACnE;QACA,OAAOiD,OAAO;MAChB,CAAC,CAAC;MACFX,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEW,aAAa,CAACN,MAAM,CAAC;MAC3DC,QAAQ,GAAGK,aAAa;IAC1B,CAAC,MAAM;MACLL,QAAQ,GAAGC,aAAa;IAC1B;;IAEA;IACA,IAAI5C,eAAe,KAAK,KAAK,EAAE;MAC7B2C,QAAQ,GAAGA,QAAQ,CAACE,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAChB,OAAO,KAAK9B,eAAe,CAAC;MACtEoC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEM,QAAQ,CAACD,MAAM,CAAC;IAC1D;;IAEA;IACA,IAAIxC,UAAU,CAACiD,IAAI,CAAC,CAAC,EAAE;MACrB,MAAMC,WAAW,GAAGlD,UAAU,CAACmD,WAAW,CAAC,CAAC;MAC5CV,QAAQ,GAAGA,QAAQ,CAACE,MAAM,CAACC,KAAK;QAAA,IAAAQ,YAAA,EAAAC,cAAA,EAAAC,YAAA;QAAA,OAC9B,EAAAF,YAAA,GAAAR,KAAK,CAACW,KAAK,cAAAH,YAAA,uBAAXA,YAAA,CAAaD,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,WAAW,CAAC,OAAAG,cAAA,GAChDT,KAAK,CAAChB,OAAO,cAAAyB,cAAA,uBAAbA,cAAA,CAAeF,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,WAAW,CAAC,OAAAI,YAAA,GAClDV,KAAK,CAACa,KAAK,cAAAH,YAAA,uBAAXA,YAAA,CAAaH,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,WAAW,CAAC;MAAA,CAClD,CAAC;MACDhB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEM,QAAQ,CAACD,MAAM,CAAC;IACzD;;IAEA;IACA,MAAMkB,MAAM,GAAG,CAAC,GAAGjB,QAAQ,CAAC,CAACkB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC1C,QAAQ3D,MAAM;QACZ,KAAK,QAAQ;UACX,OAAO,IAAI4D,IAAI,CAACD,CAAC,CAACE,SAAS,IAAI,CAAC,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,IAAI,CAAC,CAAC;QAChE,KAAK,QAAQ;UACX,OAAO,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,IAAI,CAAC,CAAC,GAAG,IAAID,IAAI,CAACD,CAAC,CAACE,SAAS,IAAI,CAAC,CAAC;QAChE,KAAK,OAAO;UACV,OAAO,CAACH,CAAC,CAACL,KAAK,IAAI,EAAE,EAAES,aAAa,CAACH,CAAC,CAACN,KAAK,IAAI,EAAE,CAAC;QACrD,KAAK,SAAS;UACZ,OAAO,CAACK,CAAC,CAAChC,OAAO,IAAI,EAAE,EAAEoC,aAAa,CAACH,CAAC,CAACjC,OAAO,IAAI,EAAE,CAAC;QACzD;UACE,OAAO,CAAC;MACZ;IACF,CAAC,CAAC;IAEFM,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEuB,MAAM,CAAClB,MAAM,CAAC;IACtD,OAAOkB,MAAM;EACf,CAAC,EAAE,CAACvE,MAAM,EAAEa,UAAU,EAAEE,MAAM,EAAET,aAAa,EAAEG,aAAa,EAAEE,eAAe,CAAC,CAAC;;EAE/E;EACA,MAAMmE,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,MAAMtB,KAAK,GAAGN,uBAAuB,CAAC4B,KAAK,CAAC;IAE5C7D,oBAAoB,CAAC6D,KAAK,CAAC;IAC3B3D,mBAAmB,CAAC,CAAC2D,KAAK,CAAC,CAAC;IAC5BzD,kBAAkB,CAAC,KAAK,CAAC;IACzBE,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,IAAIiC,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEuB,QAAQ,KAAKvB,KAAK,CAACuB,QAAQ,CAACX,QAAQ,CAAC,eAAe,CAAC,IAAIZ,KAAK,CAACuB,QAAQ,CAACX,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,IAAI;QACF,MAAMY,SAAS,GAAG,MAAMC,iBAAiB,CAACzB,KAAK,CAACuB,QAAQ,CAAC;QACzDvB,KAAK,CAAC0B,cAAc,GAAGF,SAAS;MAClC,CAAC,CAAC,OAAO7E,KAAK,EAAE;QACd2C,OAAO,CAACqC,IAAI,CAAC,8CAA8C,CAAC;QAC5D3B,KAAK,CAAC0B,cAAc,GAAG1B,KAAK,CAACuB,QAAQ;MACvC;IACF;EACF,CAAC;EAED,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5BjE,mBAAmB,CAAC,EAAE,CAAC;IACvBF,oBAAoB,CAAC,IAAI,CAAC;IAC1BI,kBAAkB,CAAC,KAAK,CAAC;IACzBE,aAAa,CAAC,IAAI,CAAC;IACnB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAAC6D,KAAK,CAAC,CAAC;IAClB;EACF,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjCjE,kBAAkB,CAAC,CAACD,eAAe,CAAC;EACtC,CAAC;;EAED;EACA,MAAM6D,iBAAiB,GAAG,MAAOF,QAAQ,IAAK;IAC5C,IAAI,CAACA,QAAQ,EAAE,OAAOA,QAAQ;;IAE9B;IACA,IAAIA,QAAQ,CAACX,QAAQ,CAAC,eAAe,CAAC,IAAIW,QAAQ,CAACX,QAAQ,CAAC,KAAK,CAAC,EAAE;MAClE,IAAI;QACF,MAAM1B,QAAQ,GAAG,MAAM6C,KAAK,CAAE,6DAA4DC,kBAAkB,CAACT,QAAQ,CAAE,EAAC,EAAE;UACxHU,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAI,CAAChD,QAAQ,CAACiD,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAE,uBAAsBlD,QAAQ,CAACmD,MAAO,EAAC,CAAC;QAC3D;QAEA,MAAMlD,IAAI,GAAG,MAAMD,QAAQ,CAACoD,IAAI,CAAC,CAAC;QAElC,IAAInD,IAAI,CAACC,OAAO,IAAID,IAAI,CAACqC,SAAS,EAAE;UAClClC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;UAC5C,OAAOJ,IAAI,CAACqC,SAAS;QACvB,CAAC,MAAM;UACLlC,OAAO,CAACqC,IAAI,CAAC,+CAA+C,EAAExC,IAAI,CAAC;UACnE,OAAOoC,QAAQ;QACjB;MACF,CAAC,CAAC,OAAO5E,KAAK,EAAE;QACd2C,OAAO,CAAC3C,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,OAAO4E,QAAQ;MACjB;IACF;IAEA,OAAOA,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMgB,eAAe,GAAIvC,KAAK,IAAK;IACjC,IAAIA,KAAK,CAACwC,SAAS,EAAE;MACnB,OAAOxC,KAAK,CAACwC,SAAS;IACxB;IAEA,IAAIxC,KAAK,CAACyC,OAAO,IAAI,CAACzC,KAAK,CAACyC,OAAO,CAAC7B,QAAQ,CAAC,eAAe,CAAC,EAAE;MAC7D,IAAI8B,OAAO,GAAG1C,KAAK,CAACyC,OAAO;MAC3B,IAAIC,OAAO,CAAC9B,QAAQ,CAAC,aAAa,CAAC,IAAI8B,OAAO,CAAC9B,QAAQ,CAAC,UAAU,CAAC,EAAE;QACnE,MAAM+B,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;QACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;MACtC;MACA,OAAQ,8BAA6BA,OAAQ,oBAAmB;IAClE;IAEA,OAAO,0BAA0B;EACnC,CAAC;;EAED;EACAjJ,SAAS,CAAC,MAAM;IACdmF,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAEjBnF,SAAS,CAAC,MAAM;IACd,IAAI2C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEW,KAAK,EAAE;MACfD,gBAAgB,CAACV,IAAI,CAACW,KAAK,CAAC;IAC9B;IACA,IAAIX,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgE,KAAK,EAAE;MACfnD,gBAAgB,CAACb,IAAI,CAACgE,KAAK,CAAC;IAC9B;EACF,CAAC,EAAE,CAAChE,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMwG,iBAAiB,GAAGA,CAAA,KAAM;IAC9BvF,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;EAED,MAAMwF,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACAjE,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMkE,cAAc,GAAGA,CAAA,KAAM;IAC3BzF,aAAa,CAAC,EAAE,CAAC;IACjBF,kBAAkB,CAAC,KAAK,CAAC;IACzBF,gBAAgB,CAAC,KAAK,CAAC;IACvB2B,WAAW,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAMmE,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI3E,UAAU,CAACiC,IAAI,CAAC,CAAC,EAAE;MACrB,MAAM2C,OAAO,GAAG;QACdC,EAAE,EAAE/B,IAAI,CAACgC,GAAG,CAAC,CAAC;QACdC,IAAI,EAAE/E,UAAU;QAChBgF,MAAM,EAAE,CAAAhH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiH,IAAI,KAAI,WAAW;QACjCC,SAAS,EAAE,IAAIpC,IAAI,CAAC,CAAC,CAACqC,WAAW,CAAC,CAAC;QACnCC,OAAO,EAAE;MACX,CAAC;MACDrF,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAE8E,OAAO,CAAC,CAAC;MACnC3E,aAAa,CAAC,EAAE,CAAC;IACnB;EACF,CAAC;EAED,MAAMoF,cAAc,GAAIC,SAAS,IAAK;IACpC,IAAIlF,SAAS,CAAC6B,IAAI,CAAC,CAAC,EAAE;MACpB,MAAMsD,KAAK,GAAG;QACZV,EAAE,EAAE/B,IAAI,CAACgC,GAAG,CAAC,CAAC;QACdC,IAAI,EAAE3E,SAAS;QACf4E,MAAM,EAAE,CAAAhH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiH,IAAI,KAAI,WAAW;QACjCC,SAAS,EAAE,IAAIpC,IAAI,CAAC,CAAC,CAACqC,WAAW,CAAC;MACpC,CAAC;MAEDpF,WAAW,CAACD,QAAQ,CAAC0F,GAAG,CAACZ,OAAO,IAC9BA,OAAO,CAACC,EAAE,KAAKS,SAAS,GACpB;QAAE,GAAGV,OAAO;QAAEQ,OAAO,EAAE,CAAC,GAAGR,OAAO,CAACQ,OAAO,EAAEG,KAAK;MAAE,CAAC,GACpDX,OACN,CAAC,CAAC;MACFvE,YAAY,CAAC,EAAE,CAAC;MAChBF,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,oBACEtC,OAAA;IAAK8C,SAAS,EAAC,yBAAyB;IAAA8E,QAAA,gBAEtC5H,OAAA;MAAK8C,SAAS,EAAC,sBAAsB;MAAA8E,QAAA,eACnC5H,OAAA;QAAK8C,SAAS,EAAC,gBAAgB;QAAA8E,QAAA,gBAC7B5H,OAAA;UAAK8C,SAAS,EAAC,aAAa;UAAA8E,QAAA,gBAC1B5H,OAAA;YAAK8C,SAAS,EAAC,aAAa;YAAA8E,QAAA,eAC1B5H,OAAA,CAACpB,OAAO;cAAAiJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACNhI,OAAA;YAAK8C,SAAS,EAAC,aAAa;YAAA8E,QAAA,gBAC1B5H,OAAA;cAAA4H,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBhI,OAAA;cAAA4H,QAAA,EAAG;YAAiD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhI,OAAA;UAAK8C,SAAS,EAAC,eAAe;UAAA8E,QAAA,gBAC5B5H,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAA8E,QAAA,gBAC5B5H,OAAA;cAAM8C,SAAS,EAAC,aAAa;cAAA8E,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3ChI,OAAA;cAAM8C,SAAS,EAAC,aAAa;cAAA8E,QAAA,EAAEhH,aAAa,CAACqH,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGtH,aAAa,CAACuH,KAAK,CAAC,CAAC;YAAC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC,eACNhI,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAA8E,QAAA,gBAC5B5H,OAAA;cAAM8C,SAAS,EAAC,aAAa;cAAA8E,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDhI,OAAA;cAAM8C,SAAS,EAAC,aAAa;cAAA8E,QAAA,EAC1B,CAAAzH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,KAAK,MAAK,SAAS,GAAI,SAAQ,CAAAX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,KAAK,KAAI,KAAM,EAAC,GAC3D,CAAAhE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,KAAK,MAAK,WAAW,GAAI,QAAO,CAAAX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,KAAK,KAAI,KAAM,EAAC,GAC5D,CAAAhE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,KAAK,MAAK,SAAS,GAAI,QAAO,CAAAX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,KAAK,KAAI,KAAM,EAAC,GAC1D;YAAS;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhI,OAAA;MAAK8C,SAAS,EAAC,uBAAuB;MAAA8E,QAAA,gBAEpC5H,OAAA;QAAK8C,SAAS,EAAC,gBAAgB;QAAA8E,QAAA,gBAC7B5H,OAAA;UAAK8C,SAAS,EAAC,cAAc;UAAA8E,QAAA,gBAE3B5H,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAA8E,QAAA,gBAC5B5H,OAAA;cAAO8C,SAAS,EAAC,eAAe;cAAA8E,QAAA,gBAC9B5H,OAAA,CAACnB,QAAQ;gBAAAgJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,SAEd;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhI,OAAA;cACEoI,KAAK,EAAExH,aAAc;cACrByH,QAAQ,EAAGC,CAAC,IAAKzH,gBAAgB,CAACyH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAClDtF,SAAS,EAAC,6BAA6B;cAAA8E,QAAA,gBAEvC5H,OAAA;gBAAQoI,KAAK,EAAC,SAAS;gBAAAR,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9ChI,OAAA;gBAAQoI,KAAK,EAAC,WAAW;gBAAAR,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClDhI,OAAA;gBAAQoI,KAAK,EAAC,SAAS;gBAAAR,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNhI,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAA8E,QAAA,gBAC5B5H,OAAA;cAAO8C,SAAS,EAAC,eAAe;cAAA8E,QAAA,gBAC9B5H,OAAA,CAACjB,QAAQ;gBAAA8I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAEd;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhI,OAAA;cACEoI,KAAK,EAAErH,aAAc;cACrBsH,QAAQ,EAAGC,CAAC,IAAKtH,gBAAgB,CAACsH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAClDtF,SAAS,EAAC,6BAA6B;cAAA8E,QAAA,gBAEvC5H,OAAA;gBAAQoI,KAAK,EAAC,KAAK;gBAAAR,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACvCvF,gBAAgB,CAACkF,GAAG,CAAEa,GAAG,iBACxBxI,OAAA;gBAAkBoI,KAAK,EAAEI,GAAI;gBAAAZ,QAAA,EAC1BhH,aAAa,KAAK,SAAS,GAAI,SAAQ4H,GAAI,EAAC,GAAI,QAAOA,GAAI;cAAC,GADlDA,GAAG;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAER,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNhI,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAA8E,QAAA,gBAC5B5H,OAAA;cAAO8C,SAAS,EAAC,eAAe;cAAA8E,QAAA,gBAC9B5H,OAAA,CAACjB,QAAQ;gBAAA8I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEd;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhI,OAAA;cACEoI,KAAK,EAAEnH,eAAgB;cACvBoH,QAAQ,EAAGC,CAAC,IAAKpH,kBAAkB,CAACoH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACpDtF,SAAS,EAAC,+BAA+B;cAAA8E,QAAA,gBAEzC5H,OAAA;gBAAQoI,KAAK,EAAC,KAAK;gBAAAR,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACxCtF,iBAAiB,CAACiF,GAAG,CAAE5E,OAAO,iBAC7B/C,OAAA;gBAAsBoI,KAAK,EAAErF,OAAQ;gBAAA6E,QAAA,EAClC7E;cAAO,GADGA,OAAO;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEZ,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNhI,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAA8E,QAAA,gBAC5B5H,OAAA;cAAO8C,SAAS,EAAC,eAAe;cAAA8E,QAAA,gBAC9B5H,OAAA,CAAChB,eAAe;gBAAA6I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,QAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhI,OAAA;cACEoI,KAAK,EAAE/G,MAAO;cACdgH,QAAQ,EAAGC,CAAC,IAAKhH,SAAS,CAACgH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC3CtF,SAAS,EAAC,4BAA4B;cAAA8E,QAAA,gBAEtC5H,OAAA;gBAAQoI,KAAK,EAAC,QAAQ;gBAAAR,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5ChI,OAAA;gBAAQoI,KAAK,EAAC,QAAQ;gBAAAR,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5ChI,OAAA;gBAAQoI,KAAK,EAAC,OAAO;gBAAAR,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxChI,OAAA;gBAAQoI,KAAK,EAAC,SAAS;gBAAAR,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhI,OAAA;UAAK8C,SAAS,EAAC,YAAY;UAAA8E,QAAA,gBACzB5H,OAAA;YAAK8C,SAAS,EAAC,kBAAkB;YAAA8E,QAAA,gBAC/B5H,OAAA,CAAClB,QAAQ;cAACgE,SAAS,EAAC;YAAa;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpChI,OAAA;cACEyI,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,8CAA8C;cAC1DN,KAAK,EAAEjH,UAAW;cAClBkH,QAAQ,EAAGC,CAAC,IAAKlH,aAAa,CAACkH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/CtF,SAAS,EAAC;YAAc;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,EACD7G,UAAU,iBACTnB,OAAA;cAAQ2I,OAAO,EAAEhC,iBAAkB;cAAC7D,SAAS,EAAC,kBAAkB;cAAA8E,QAAA,gBAC9D5H,OAAA,CAACR,GAAG;gBAAAqI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAET;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENhI,OAAA;YAAQ2I,OAAO,EAAE/B,aAAc;YAAC9D,SAAS,EAAC,aAAa;YAAA8E,QAAA,gBACrD5H,OAAA,CAACf,UAAU;cAAA4I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEhB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLxH,OAAO,gBACNR,OAAA;QAAK8C,SAAS,EAAC,eAAe;QAAA8E,QAAA,gBAC5B5H,OAAA;UAAK8C,SAAS,EAAC;QAAiB;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvChI,OAAA;UAAA4H,QAAA,EAAG;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,GACJtH,KAAK,gBACPV,OAAA;QAAK8C,SAAS,EAAC,aAAa;QAAA8E,QAAA,gBAC1B5H,OAAA,CAACP,eAAe;UAACqD,SAAS,EAAC;QAAY;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1ChI,OAAA;UAAA4H,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BhI,OAAA;UAAA4H,QAAA,EAAIlH;QAAK;UAAAmH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdhI,OAAA;UAAQ2I,OAAO,EAAEhG,WAAY;UAACG,SAAS,EAAC,WAAW;UAAA8E,QAAA,EAAC;QAEpD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJvE,uBAAuB,CAACE,MAAM,GAAG,CAAC,gBACpC3D,OAAA;QAAK8C,SAAS,EAAC,aAAa;QAAA8E,QAAA,EACzBnE,uBAAuB,CAACkE,GAAG,CAAC,CAAC5D,KAAK,EAAEsB,KAAK,kBACxCrF,OAAA;UAAiB8C,SAAS,EAAC,YAAY;UAAC6F,OAAO,EAAEA,CAAA,KAAMvD,eAAe,CAACC,KAAK,CAAE;UAAAuC,QAAA,gBAC5E5H,OAAA;YAAK8C,SAAS,EAAC,sBAAsB;YAAA8E,QAAA,gBACnC5H,OAAA;cACE4I,GAAG,EAAEtC,eAAe,CAACvC,KAAK,CAAE;cAC5B8E,GAAG,EAAE9E,KAAK,CAACW,KAAM;cACjB5B,SAAS,EAAC,iBAAiB;cAC3BgG,OAAO,EAAGR,CAAC,IAAK;gBACd;gBACA,IAAIvE,KAAK,CAACyC,OAAO,IAAI,CAACzC,KAAK,CAACyC,OAAO,CAAC7B,QAAQ,CAAC,eAAe,CAAC,EAAE;kBAC7D;kBACA,IAAI8B,OAAO,GAAG1C,KAAK,CAACyC,OAAO;kBAC3B,IAAIC,OAAO,CAAC9B,QAAQ,CAAC,aAAa,CAAC,IAAI8B,OAAO,CAAC9B,QAAQ,CAAC,UAAU,CAAC,EAAE;oBACnE,MAAM+B,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;oBACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;kBACtC;kBAEA,MAAMsC,SAAS,GAAG,CACf,8BAA6BtC,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,cAAa,EACnD,0BAA0B,CAC3B;kBAED,MAAMuC,UAAU,GAAGV,CAAC,CAACC,MAAM,CAACK,GAAG;kBAC/B,MAAMK,YAAY,GAAGF,SAAS,CAACG,SAAS,CAACC,GAAG,IAAIH,UAAU,CAACrE,QAAQ,CAACwE,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;kBAE1F,IAAIJ,YAAY,GAAGF,SAAS,CAACpF,MAAM,GAAG,CAAC,EAAE;oBACvC2E,CAAC,CAACC,MAAM,CAACK,GAAG,GAAGG,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;kBAC5C;gBACF,CAAC,MAAM;kBACLX,CAAC,CAACC,MAAM,CAACK,GAAG,GAAG,0BAA0B;gBAC3C;cACF;YAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFhI,OAAA;cAAK8C,SAAS,EAAC,cAAc;cAAA8E,QAAA,eAC3B5H,OAAA,CAAC9B,YAAY;gBAAC4E,SAAS,EAAC;cAAW;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACNhI,OAAA;cAAK8C,SAAS,EAAC,gBAAgB;cAAA8E,QAAA,EAC5B7D,KAAK,CAACuF,QAAQ,IAAI;YAAO;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,EACLjE,KAAK,CAACwF,SAAS,IAAIxF,KAAK,CAACwF,SAAS,CAAC5F,MAAM,GAAG,CAAC,iBAC5C3D,OAAA;cAAK8C,SAAS,EAAC,gBAAgB;cAAA8E,QAAA,gBAC7B5H,OAAA,CAACN,YAAY;gBAAAmI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,MAElB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENhI,OAAA;YAAK8C,SAAS,EAAC,oBAAoB;YAAA8E,QAAA,gBACjC5H,OAAA;cAAI8C,SAAS,EAAC,aAAa;cAAA8E,QAAA,EAAE7D,KAAK,CAACW;YAAK;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9ChI,OAAA;cAAK8C,SAAS,EAAC,YAAY;cAAA8E,QAAA,gBACzB5H,OAAA;gBAAM8C,SAAS,EAAC,eAAe;gBAAA8E,QAAA,EAAE7D,KAAK,CAAChB;cAAO;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtDhI,OAAA;gBAAM8C,SAAS,EAAC,aAAa;gBAAA8E,QAAA,EAC1BhH,aAAa,KAAK,SAAS,GAAI,SAAQmD,KAAK,CAACjB,SAAS,IAAIiB,KAAK,CAACI,KAAM,EAAC,GAAI,QAAOJ,KAAK,CAACjB,SAAS,IAAIiB,KAAK,CAACI,KAAM;cAAC;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNhI,OAAA;cAAK8C,SAAS,EAAC,YAAY;cAAA8E,QAAA,gBACzB5H,OAAA;gBAAM8C,SAAS,EAAC,WAAW;gBAAA8E,QAAA,EAAE7D,KAAK,CAACjD;cAAK;gBAAA+G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC/CjE,KAAK,CAACa,KAAK,iBAAI5E,OAAA;gBAAM8C,SAAS,EAAC,WAAW;gBAAA8E,QAAA,EAAE7D,KAAK,CAACa;cAAK;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC/DjE,KAAK,CAACyF,eAAe,IAAIzF,KAAK,CAACyF,eAAe,KAAKzF,KAAK,CAACjB,SAAS,iBACjE9C,OAAA;gBAAM8C,SAAS,EAAC,YAAY;gBAAA8E,QAAA,GAAC,cACf,EAAChH,aAAa,KAAK,SAAS,GAAI,SAAQmD,KAAK,CAACyF,eAAgB,EAAC,GAAI,QAAOzF,KAAK,CAACyF,eAAgB,EAAC;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzG,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAjEE3C,KAAK;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkEV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENhI,OAAA;QAAK8C,SAAS,EAAC,aAAa;QAAA8E,QAAA,gBAC1B5H,OAAA,CAAC5B,eAAe;UAAC0E,SAAS,EAAC;QAAY;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1ChI,OAAA;UAAA4H,QAAA,EAAI;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBhI,OAAA;UAAA4H,QAAA,EAAG;QAA0D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACjEhI,OAAA;UAAG8C,SAAS,EAAC,YAAY;UAAA8E,QAAA,EAAC;QAA2C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLvG,gBAAgB,CAACkC,MAAM,GAAG,CAAC,IAAIpC,iBAAiB,KAAK,IAAI,iBACxDvB,OAAA;MAAK8C,SAAS,EAAG,iBAAgBnB,eAAe,GAAG,UAAU,GAAG,EAAG,EAAE;MAACgH,OAAO,EAAGL,CAAC,IAAK;QACpF,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACmB,aAAa,EAAE9D,eAAe,CAAC,CAAC;MACrD,CAAE;MAAAiC,QAAA,eACA5H,OAAA;QAAK8C,SAAS,EAAG,eAAcnB,eAAe,GAAG,UAAU,GAAG,EAAG,EAAE;QAAAiG,QAAA,EAChE,CAAC,MAAM;UACN,MAAM7D,KAAK,GAAGN,uBAAuB,CAAClC,iBAAiB,CAAC;UACxD,IAAI,CAACwC,KAAK,EAAE,oBAAO/D,OAAA;YAAA4H,QAAA,EAAK;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;UAE7C,oBACEhI,OAAA;YAAK8C,SAAS,EAAC,eAAe;YAAA8E,QAAA,gBAC5B5H,OAAA;cAAK8C,SAAS,EAAC,cAAc;cAAA8E,QAAA,gBAC3B5H,OAAA;gBAAK8C,SAAS,EAAC,YAAY;gBAAA8E,QAAA,gBACzB5H,OAAA;kBAAI8C,SAAS,EAAC,aAAa;kBAAA8E,QAAA,EAAE7D,KAAK,CAACW;gBAAK;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9ChI,OAAA;kBAAK8C,SAAS,EAAC,YAAY;kBAAA8E,QAAA,gBACzB5H,OAAA;oBAAM8C,SAAS,EAAC,eAAe;oBAAA8E,QAAA,EAAE7D,KAAK,CAAChB;kBAAO;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtDhI,OAAA;oBAAM8C,SAAS,EAAC,aAAa;oBAAA8E,QAAA,GAAC,QAAM,EAAC7D,KAAK,CAACjB,SAAS;kBAAA;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC3DjE,KAAK,CAACjD,KAAK,iBAAId,OAAA;oBAAM8C,SAAS,EAAC,aAAa;oBAAA8E,QAAA,EAAE7D,KAAK,CAACjD;kBAAK;oBAAA+G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNhI,OAAA;gBAAK8C,SAAS,EAAC,gBAAgB;gBAAA8E,QAAA,gBAC7B5H,OAAA;kBACE8C,SAAS,EAAC,wBAAwB;kBAClC6F,OAAO,EAAE9C,oBAAqB;kBAC9BnB,KAAK,EAAE/C,eAAe,GAAG,iBAAiB,GAAG,kBAAmB;kBAAAiG,QAAA,EAE/DjG,eAAe,gBAAG3B,OAAA,CAACrB,UAAU;oBAAAkJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGhI,OAAA,CAACtB,QAAQ;oBAAAmJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,eACThI,OAAA;kBACE8C,SAAS,EAAC,uBAAuB;kBACjC6F,OAAO,EAAEhD,eAAgB;kBACzBjB,KAAK,EAAC,aAAa;kBAAAkD,QAAA,eAEnB5H,OAAA,CAACzB,OAAO;oBAAAsJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNhI,OAAA;cAAK8C,SAAS,EAAC,iBAAiB;cAAA8E,QAAA,EAC7B7D,KAAK,CAACuB,QAAQ,gBACbtF,OAAA;gBAAK0J,KAAK,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,MAAM;kBAAEC,YAAY,EAAE;gBAAM,CAAE;gBAAAjC,QAAA,gBACrE5H,OAAA;kBACE8J,GAAG,EAAGA,GAAG,IAAK9H,WAAW,CAAC8H,GAAG,CAAE;kBAC/BC,QAAQ;kBACRC,QAAQ;kBACRC,WAAW;kBACXC,OAAO,EAAC,UAAU;kBAClBC,KAAK,EAAC,MAAM;kBACZC,MAAM,EAAC,KAAK;kBACZC,MAAM,EAAE/D,eAAe,CAACvC,KAAK,CAAE;kBAC/B2F,KAAK,EAAE;oBACLS,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,OAAO;oBACfE,eAAe,EAAE;kBACnB,CAAE;kBACFxB,OAAO,EAAGR,CAAC,IAAK;oBACdxG,aAAa,CAAE,yBAAwBiC,KAAK,CAACW,KAAM,mCAAkC,CAAC;kBACxF,CAAE;kBACF6F,SAAS,EAAEA,CAAA,KAAM;oBACfzI,aAAa,CAAC,IAAI,CAAC;kBACrB,CAAE;kBACF0I,WAAW,EAAEA,CAAA,KAAM;oBACjBnH,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;kBACzC,CAAE;kBACFmH,WAAW,EAAC,WAAW;kBAAA7C,QAAA,gBAGvB5H,OAAA;oBAAQ4I,GAAG,EAAE7E,KAAK,CAAC0B,cAAc,IAAI1B,KAAK,CAACuB,QAAS;oBAACmD,IAAI,EAAC;kBAAW;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAGvEjE,KAAK,CAACwF,SAAS,IAAIxF,KAAK,CAACwF,SAAS,CAAC5F,MAAM,GAAG,CAAC,IAAII,KAAK,CAACwF,SAAS,CAAC5B,GAAG,CAAC,CAAC+C,QAAQ,EAAErF,KAAK,kBACpFrF,OAAA;oBAEE2K,IAAI,EAAC,WAAW;oBAChB/B,GAAG,EAAE8B,QAAQ,CAACvB,GAAI;oBAClByB,OAAO,EAAEF,QAAQ,CAACG,QAAS;oBAC3BC,KAAK,EAAEJ,QAAQ,CAACK,YAAa;oBAC7BC,OAAO,EAAEN,QAAQ,CAACO,SAAS,IAAI5F,KAAK,KAAK;kBAAE,GALrC,GAAEqF,QAAQ,CAACG,QAAS,IAAGxF,KAAM,EAAC;oBAAAwC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAMrC,CACF,CAAC,EAAC,8CAGL;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAGPjE,KAAK,CAACwF,SAAS,IAAIxF,KAAK,CAACwF,SAAS,CAAC5F,MAAM,GAAG,CAAC,iBAC5C3D,OAAA;kBAAK8C,SAAS,EAAC,oBAAoB;kBAAA8E,QAAA,gBACjC5H,OAAA,CAACN,YAAY;oBAACoD,SAAS,EAAC;kBAAe;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1ChI,OAAA;oBAAA4H,QAAA,GAAM,yBAAuB,EAAC7D,KAAK,CAACwF,SAAS,CAAC5F,MAAM,EAAC,cAAY;kBAAA;oBAAAkE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CACN,EAGAnG,UAAU,iBACT7B,OAAA;kBAAK8C,SAAS,EAAC,qBAAqB;kBAAA8E,QAAA,eAClC5H,OAAA;oBAAK8C,SAAS,EAAC,eAAe;oBAAA8E,QAAA,gBAC5B5H,OAAA,CAACP,eAAe;sBAACqD,SAAS,EAAC;oBAAY;sBAAA+E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1ChI,OAAA;sBAAA4H,QAAA,EAAI/F;oBAAU;sBAAAgG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnBhI,OAAA;sBAAQ2I,OAAO,EAAEA,CAAA,KAAM7G,aAAa,CAAC,IAAI,CAAE;sBAACgB,SAAS,EAAC,mBAAmB;sBAAA8E,QAAA,EAAC;oBAE1E;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,GACNjE,KAAK,CAACyC,OAAO;cAAA;cACf;cACAxG,OAAA;gBACE4I,GAAG,EAAG,iCAAgC7E,KAAK,CAACyC,OAAQ,mBAAmB;gBACvE9B,KAAK,EAAEX,KAAK,CAACW,KAAM;gBACnBwG,WAAW,EAAC,GAAG;gBACfC,eAAe;gBACfrI,SAAS,EAAC,cAAc;gBACxBsI,MAAM,EAAEA,CAAA,KAAM/H,OAAO,CAACC,GAAG,CAAC,yBAAyB;cAAE;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,gBAEVhI,OAAA;gBAAK8C,SAAS,EAAC,aAAa;gBAAA8E,QAAA,gBAC1B5H,OAAA;kBAAK8C,SAAS,EAAC,YAAY;kBAAA8E,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpChI,OAAA;kBAAA4H,QAAA,EAAI;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1BhI,OAAA;kBAAA4H,QAAA,EAAI/F,UAAU,IAAI;gBAA4C;kBAAAgG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnEhI,OAAA;kBAAK8C,SAAS,EAAC,eAAe;kBAAA8E,QAAA,eAC5B5H,OAAA;oBACEqL,IAAI,EAAEtH,KAAK,CAAC0B,cAAc,IAAI1B,KAAK,CAACuB,QAAS;oBAC7CiD,MAAM,EAAC,QAAQ;oBACf+C,GAAG,EAAC,qBAAqB;oBACzBxI,SAAS,EAAC,mBAAmB;oBAAA8E,QAAA,EAC9B;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGL,CAACrG,eAAe,iBACf3B,OAAA;cAAK8C,SAAS,EAAC,kBAAkB;cAAA8E,QAAA,gBAC/B5H,OAAA;gBAAI8C,SAAS,EAAC,gBAAgB;gBAAA8E,QAAA,gBAC5B5H,OAAA,CAACN,YAAY;kBAAAmI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBACJ,EAAC/F,QAAQ,CAAC0B,MAAM,EAAC,GAC/B;cAAA;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAGLhI,OAAA;gBAAK8C,SAAS,EAAC,aAAa;gBAAA8E,QAAA,eAC1B5H,OAAA;kBAAK8C,SAAS,EAAC,yBAAyB;kBAAA8E,QAAA,gBACtC5H,OAAA;oBACEoI,KAAK,EAAEjG,UAAW;oBAClBkG,QAAQ,EAAGC,CAAC,IAAKlG,aAAa,CAACkG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;oBAC/CM,WAAW,EAAC,yCAAyC;oBACrD5F,SAAS,EAAC,eAAe;oBACzByI,IAAI,EAAC;kBAAG;oBAAA1D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACFhI,OAAA;oBACE2I,OAAO,EAAE7B,gBAAiB;oBAC1BhE,SAAS,EAAC,oBAAoB;oBAC9B0I,QAAQ,EAAE,CAACrJ,UAAU,CAACiC,IAAI,CAAC,CAAE;oBAAAwD,QAAA,EAC9B;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNhI,OAAA;gBAAK8C,SAAS,EAAC,eAAe;gBAAA8E,QAAA,EAC3B3F,QAAQ,CAAC0B,MAAM,KAAK,CAAC,gBACpB3D,OAAA;kBAAK8C,SAAS,EAAC,aAAa;kBAAA8E,QAAA,gBAC1B5H,OAAA,CAACN,YAAY;oBAAAmI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChBhI,OAAA;oBAAA4H,QAAA,EAAG;kBAAqD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC,GAEN/F,QAAQ,CAAC0F,GAAG,CAAEZ,OAAO,iBACnB/G,OAAA;kBAAsB8C,SAAS,EAAC,SAAS;kBAAA8E,QAAA,gBACvC5H,OAAA;oBAAK8C,SAAS,EAAC,gBAAgB;oBAAA8E,QAAA,gBAC7B5H,OAAA;sBAAM8C,SAAS,EAAC,gBAAgB;sBAAA8E,QAAA,EAAEb,OAAO,CAACI;oBAAM;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACxDhI,OAAA;sBAAM8C,SAAS,EAAC,cAAc;sBAAA8E,QAAA,EAC3B,IAAI3C,IAAI,CAAC8B,OAAO,CAACM,SAAS,CAAC,CAACoE,kBAAkB,CAAC;oBAAC;sBAAA5D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNhI,OAAA;oBAAK8C,SAAS,EAAC,cAAc;oBAAA8E,QAAA,EAAEb,OAAO,CAACG;kBAAI;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClDhI,OAAA;oBAAK8C,SAAS,EAAC,iBAAiB;oBAAA8E,QAAA,eAC9B5H,OAAA;sBACE2I,OAAO,EAAEA,CAAA,KAAMrG,aAAa,CAACyE,OAAO,CAACC,EAAE,CAAE;sBACzClE,SAAS,EAAC,WAAW;sBAAA8E,QAAA,EACtB;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,EAGL3F,UAAU,KAAK0E,OAAO,CAACC,EAAE,iBACxBhH,OAAA;oBAAK8C,SAAS,EAAC,uBAAuB;oBAAA8E,QAAA,gBACpC5H,OAAA;sBACEoI,KAAK,EAAE7F,SAAU;sBACjB8F,QAAQ,EAAGC,CAAC,IAAK9F,YAAY,CAAC8F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;sBAC9CM,WAAW,EAAC,kBAAkB;sBAC9B5F,SAAS,EAAC,aAAa;sBACvByI,IAAI,EAAC;oBAAG;sBAAA1D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC,eACFhI,OAAA;sBAAK8C,SAAS,EAAC,eAAe;sBAAA8E,QAAA,gBAC5B5H,OAAA;wBACE2I,OAAO,EAAEA,CAAA,KAAMnB,cAAc,CAACT,OAAO,CAACC,EAAE,CAAE;wBAC1ClE,SAAS,EAAC,kBAAkB;wBAC5B0I,QAAQ,EAAE,CAACjJ,SAAS,CAAC6B,IAAI,CAAC,CAAE;wBAAAwD,QAAA,EAC7B;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACThI,OAAA;wBACE2I,OAAO,EAAEA,CAAA,KAAM;0BACbrG,aAAa,CAAC,IAAI,CAAC;0BACnBE,YAAY,CAAC,EAAE,CAAC;wBAClB,CAAE;wBACFM,SAAS,EAAC,kBAAkB;wBAAA8E,QAAA,EAC7B;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,EAGAjB,OAAO,CAACQ,OAAO,CAAC5D,MAAM,GAAG,CAAC,iBACzB3D,OAAA;oBAAK8C,SAAS,EAAC,SAAS;oBAAA8E,QAAA,EACrBb,OAAO,CAACQ,OAAO,CAACI,GAAG,CAAED,KAAK,iBACzB1H,OAAA;sBAAoB8C,SAAS,EAAC,OAAO;sBAAA8E,QAAA,gBACnC5H,OAAA;wBAAK8C,SAAS,EAAC,cAAc;wBAAA8E,QAAA,gBAC3B5H,OAAA;0BAAM8C,SAAS,EAAC,cAAc;0BAAA8E,QAAA,EAAEF,KAAK,CAACP;wBAAM;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eACpDhI,OAAA;0BAAM8C,SAAS,EAAC,YAAY;0BAAA8E,QAAA,EACzB,IAAI3C,IAAI,CAACyC,KAAK,CAACL,SAAS,CAAC,CAACoE,kBAAkB,CAAC;wBAAC;0BAAA5D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3C,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNhI,OAAA;wBAAK8C,SAAS,EAAC,YAAY;wBAAA8E,QAAA,EAAEF,KAAK,CAACR;sBAAI;wBAAAW,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA,GAPtCN,KAAK,CAACV,EAAE;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAQb,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CACN;gBAAA,GA/DOjB,OAAO,CAACC,EAAE;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgEf,CACN;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAEV,CAAC,EAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAC9H,EAAA,CAzxBQD,YAAY;EAAA,QACFlC,WAAW,EACXD,WAAW;AAAA;AAAA4N,EAAA,GAFrBzL,YAAY;AA2xBrB,eAAeA,YAAY;AAAC,IAAAyL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}